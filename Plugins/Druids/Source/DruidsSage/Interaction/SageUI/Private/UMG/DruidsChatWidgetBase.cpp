#include "UMG/DruidsChatWidgetBase.h"
#include "Components/Widget.h"
#include "Components/TextBlock.h"
#include "Components/RichTextBlock.h"
#include "Components/Border.h"
#include "Components/BorderSlot.h"
#include "Components/Button.h"
#include "Components/PanelWidget.h"
#include "Components/PanelSlot.h"
#include "Components/VerticalBox.h"
#include "Components/VerticalBoxSlot.h"
#include "Components/HorizontalBox.h"
#include "Components/HorizontalBoxSlot.h"
#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"
#include "LogDruids.h"

UDruidsChatWidgetBase::UDruidsChatWidgetBase(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize default style
    CurrentStyle = FDruidsChatStyle();
}

void UDruidsChatWidgetBase::NativeConstruct()
{
    Super::NativeConstruct();
    
    InitializeWidget();
    
    // Apply current style
    ApplyStyle(CurrentStyle);
    
    // Notify Blueprint
    OnWidgetInitialized();
    
    bIsInitialized = true;
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetBase::NativeConstruct - Widget %s initialized"), *GetClass()->GetName());
}

void UDruidsChatWidgetBase::NativeDestruct()
{
    bIsInitialized = false;
    
    // Notify Blueprint
    OnWidgetDestroyed();
    
    CleanupWidget();
    
    Super::NativeDestruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetBase::NativeDestruct - Widget %s destroyed"), *GetClass()->GetName());
}

void UDruidsChatWidgetBase::ApplyStyle(const FDruidsChatStyle& Style)
{
    CurrentStyle = Style;
    
    // Notify Blueprint implementation
    OnStyleChanged(Style);
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyStyle - Style applied to %s"), *GetClass()->GetName());
}

void UDruidsChatWidgetBase::SetStyle(const FDruidsChatStyle& NewStyle)
{
    ApplyStyle(NewStyle);
}

void UDruidsChatWidgetBase::ApplyColorToWidget(UWidget* Widget, const FLinearColor& Color, const FString& PropertyName)
{
    if (!Widget)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetBase::ApplyColorToWidget - Widget is null"));
        return;
    }

    // Handle different widget types
    if (UTextBlock* TextBlock = Cast<UTextBlock>(Widget))
    {
        if (PropertyName == TEXT("ColorAndOpacity") || PropertyName.IsEmpty())
        {
            TextBlock->SetColorAndOpacity(FSlateColor(Color));
        }
    }
    else if (URichTextBlock* RichTextBlock = Cast<URichTextBlock>(Widget))
    {
        if (PropertyName == TEXT("ColorAndOpacity") || PropertyName.IsEmpty())
        {
            RichTextBlock->SetDefaultColorAndOpacity(FSlateColor(Color));
        }
    }
    else if (UBorder* Border = Cast<UBorder>(Widget))
    {
        if (PropertyName == TEXT("BrushColor") || PropertyName.IsEmpty())
        {
            Border->SetBrushColor(Color);
        }
        else if (PropertyName == TEXT("ContentColorAndOpacity"))
        {
            Border->SetContentColorAndOpacity(Color);
        }
    }
    else if (UButton* Button = Cast<UButton>(Widget))
    {
        if (PropertyName == TEXT("BackgroundColor") || PropertyName.IsEmpty())
        {
            Button->SetBackgroundColor(Color);
        }
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyColorToWidget - Applied color to %s"), *Widget->GetClass()->GetName());
}

void UDruidsChatWidgetBase::ApplyFontToTextWidget(UWidget* TextWidget, const FSlateFontInfo& Font)
{
    if (!TextWidget)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetBase::ApplyFontToTextWidget - TextWidget is null"));
        return;
    }

    if (UTextBlock* TextBlock = Cast<UTextBlock>(TextWidget))
    {
        TextBlock->SetFont(Font);
    }
    else if (URichTextBlock* RichTextBlock = Cast<URichTextBlock>(TextWidget))
    {
        // RichTextBlock uses decorators for font styling
        // This would need to be handled through the decorator system
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetBase::ApplyFontToTextWidget - RichTextBlock font styling should be handled through decorators"));
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyFontToTextWidget - Applied font to %s"), *TextWidget->GetClass()->GetName());
}

void UDruidsChatWidgetBase::ApplyPaddingToWidget(UWidget* Widget, const FMargin& WidgetPadding)
{
    if (!Widget)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetBase::ApplyPaddingToWidget - Widget is null"));
        return;
    }

    // Apply padding through the widget's slot if it's in a panel
    if (UPanelWidget* ParentPanel = Widget->GetParent())
    {
        if (UPanelSlot* WidgetSlot = Widget->Slot)
        {
            // Try to cast to specific slot types that support padding
            if (UBorderSlot* BorderSlot = Cast<UBorderSlot>(WidgetSlot))
            {
                BorderSlot->SetPadding(WidgetPadding);
            }
            else if (UVerticalBoxSlot* VBoxSlot = Cast<UVerticalBoxSlot>(WidgetSlot))
            {
                VBoxSlot->SetPadding(WidgetPadding);
            }
            else if (UHorizontalBoxSlot* HBoxSlot = Cast<UHorizontalBoxSlot>(WidgetSlot))
            {
                HBoxSlot->SetPadding(WidgetPadding);
            }
            else if (UCanvasPanelSlot* CanvasSlot = Cast<UCanvasPanelSlot>(WidgetSlot))
            {
                // Canvas slots don't have padding, but we could set offsets
                UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyPaddingToWidget - Canvas slots don't support padding"));
            }
            else
            {
                UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyPaddingToWidget - Slot type %s doesn't support padding"),
                    *WidgetSlot->GetClass()->GetName());
            }
        }
    }

    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::ApplyPaddingToWidget - Applied padding to %s"), *Widget->GetClass()->GetName());
}

void UDruidsChatWidgetBase::InitializeWidget()
{
    // Override in derived classes for specific initialization
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::InitializeWidget - Base initialization for %s"), *GetClass()->GetName());
}

void UDruidsChatWidgetBase::CleanupWidget()
{
    // Override in derived classes for specific cleanup
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetBase::CleanupWidget - Base cleanup for %s"), *GetClass()->GetName());
}
