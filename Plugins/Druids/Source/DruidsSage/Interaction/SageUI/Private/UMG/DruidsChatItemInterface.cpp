#include "UMG/DruidsChatItemInterface.h"
#include "Components/TextBlock.h"
#include "Components/RichTextBlock.h"
#include "Components/Border.h"
#include "Components/HorizontalBox.h"
#include "Components/Button.h"
#include "LogDruids.h"

UDruidsChatItemBase::UDruidsChatItemBase(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    MessageRole = EDruidsSageChatRole::User;
    bIsStreaming = false;
}

void UDruidsChatItemBase::NativeConstruct()
{
    Super::NativeConstruct();
    
    InitializeChatItem();
    ApplyRoleBasedStyling();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatItemBase::NativeConstruct - Chat item %s constructed"), *GetClass()->GetName());
}

void UDruidsChatItemBase::NativeDestruct()
{
    CleanupChatItem();
    
    Super::NativeDestruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatItemBase::NativeDestruct - Chat item %s destructed"), *GetClass()->GetName());
}

void UDruidsChatItemBase::SetMessageContent_Implementation(const FDruidsSageChatMessage& Message)
{
    CurrentMessage = Message;
    MessageRole = Message.GetRole();
    
    UpdateMessageDisplay();
    ApplyRoleBasedStyling();
    NotifyItemUpdated();
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::SetMessageContent_Implementation - Message content set for role %d"), 
        (int32)MessageRole);
}

void UDruidsChatItemBase::UpdateFromStreamingContent_Implementation(const FString& PartialContent)
{
    if (!bIsStreaming)
    {
        bIsStreaming = true;
        OnStreamingStarted();
    }
    
    StreamingContent = PartialContent;
    
    // Update the message content display with streaming content
    if (MessageContent)
    {
        MessageContent->SetText(FText::FromString(StreamingContent));
    }
    
    NotifyItemUpdated();
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::UpdateFromStreamingContent_Implementation - Streaming content updated, length: %d"), 
        PartialContent.Len());
}

EDruidsSageChatRole UDruidsChatItemBase::GetMessageRole_Implementation() const
{
    return MessageRole;
}

void UDruidsChatItemBase::OnActionButtonClicked_Implementation(const FString& ActionData)
{
    OnActionClicked.Broadcast(ActionData);

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatItemBase::OnActionButtonClicked_Implementation - Action clicked: %s"), *ActionData);
}

void UDruidsChatItemBase::OnActionButtonClickedInternal()
{
    // For now, we'll use a placeholder action data
    // In a real implementation, you'd need to associate the button with its action data
    FString ActionData = TEXT("DefaultAction");
    OnActionButtonClicked_Implementation(ActionData);
}

void UDruidsChatItemBase::SetMessageRole(EDruidsSageChatRole Role)
{
    MessageRole = Role;
    ApplyRoleBasedStyling();
    NotifyItemUpdated();
}

void UDruidsChatItemBase::ApplyStyle(const FDruidsChatStyle& Style)
{
    Super::ApplyStyle(Style);
    
    // Apply style to chat item specific elements
    ApplyRoleBasedStyling();
    
    // Notify Blueprint implementation
    OnStyleApplied(Style);
}

void UDruidsChatItemBase::ApplyRoleBasedStyling()
{
    if (!bIsInitialized)
    {
        return;
    }
    
    // Update role label
    if (RoleLabel)
    {
        RoleLabel->SetText(FText::FromString(GetRoleDisplayName()));
        ApplyColorToWidget(RoleLabel, GetRoleColor());
        ApplyFontToTextWidget(RoleLabel, CurrentStyle.HeaderFont);
    }
    
    // Update message container styling based on role
    if (MessageContainer)
    {
        FLinearColor ContainerColor = GetRoleColor();
        ContainerColor.A = 0.1f; // Make it subtle
        ApplyColorToWidget(MessageContainer, ContainerColor, TEXT("BrushColor"));
    }
    
    // Update message content styling
    if (MessageContent)
    {
        ApplyColorToWidget(MessageContent, CurrentStyle.TextColor);
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::ApplyRoleBasedStyling - Applied styling for role %d"), (int32)MessageRole);
}

void UDruidsChatItemBase::UpdateMessageDisplay()
{
    if (!MessageContent)
    {
        return;
    }
    
    FString DisplayText;
    
    if (bIsStreaming && !StreamingContent.IsEmpty())
    {
        DisplayText = StreamingContent;
    }
    else
    {
        DisplayText = CurrentMessage.GetChatContent();
    }
    
    MessageContent->SetText(FText::FromString(DisplayText));
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::UpdateMessageDisplay - Message display updated"));
}

void UDruidsChatItemBase::CreateActionButtons(const TArray<FString>& ActionData)
{
    if (!ActionButtonContainer)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatItemBase::CreateActionButtons - ActionButtonContainer is null"));
        return;
    }
    
    // Clear existing buttons
    ActionButtonContainer->ClearChildren();
    
    // Create buttons for each action
    for (const FString& Action : ActionData)
    {
        UButton* ActionButton = NewObject<UButton>(this);
        if (ActionButton)
        {
            // Set button text (this would need a text block child in a real implementation)
            ActionButton->SetToolTipText(FText::FromString(Action));
            
            // Bind click event using a lambda to bridge the parameter difference
            ActionButton->OnClicked.AddDynamic(this, &UDruidsChatItemBase::OnActionButtonClickedInternal);
            
            // Add to container
            ActionButtonContainer->AddChild(ActionButton);
        }
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatItemBase::CreateActionButtons - Created %d action buttons"), ActionData.Num());
}

FString UDruidsChatItemBase::GetRoleDisplayName() const
{
    switch (MessageRole)
    {
    case EDruidsSageChatRole::User:
        return TEXT("User");
    case EDruidsSageChatRole::Assistant:
        return TEXT("Assistant");
    case EDruidsSageChatRole::System:
        return TEXT("System");
    default:
        return TEXT("Unknown");
    }
}

FLinearColor UDruidsChatItemBase::GetRoleColor() const
{
    switch (MessageRole)
    {
    case EDruidsSageChatRole::User:
        return CurrentStyle.PrimaryColor;
    case EDruidsSageChatRole::Assistant:
        return CurrentStyle.AccentColor;
    case EDruidsSageChatRole::System:
        return CurrentStyle.SecondaryColor;
    default:
        return CurrentStyle.TextColor;
    }
}

void UDruidsChatItemBase::InitializeChatItem()
{
    // Initialize chat item specific functionality
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::InitializeChatItem - Initializing chat item %s"), *GetClass()->GetName());
}

void UDruidsChatItemBase::CleanupChatItem()
{
    // Cleanup chat item specific functionality
    if (bIsStreaming)
    {
        bIsStreaming = false;
        OnStreamingCompleted();
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatItemBase::CleanupChatItem - Cleaning up chat item %s"), *GetClass()->GetName());
}

void UDruidsChatItemBase::NotifyItemUpdated()
{
    OnItemUpdated.Broadcast();
    OnMessageUpdated();
}
