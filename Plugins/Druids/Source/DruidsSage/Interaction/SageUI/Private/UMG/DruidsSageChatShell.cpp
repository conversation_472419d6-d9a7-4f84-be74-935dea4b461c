#include "UMG/DruidsSageChatShell.h"
#include "UMG/DruidsSageChatView.h"
#include "UMG/DruidsChatWidgetManager.h"
#include "UMG/DruidsChatWidgetFactory.h"
#include "IChatRequestHandler.h"
#include "ISageExtensionDelegator.h"
#include "Components/ListView.h"
#include "Components/Border.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "LogDruids.h"

// Static constants
const FString UDruidsSageChatShell::DefaultSessionName = TEXT("Default");
const FString UDruidsSageChatShell::SessionsDirectory = TEXT("DruidsSage/Sessions");
const FString UDruidsSageChatShell::SessionFileExtension = TEXT(".json");

UDruidsSageChatShell::UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    CurrentChatView = nullptr;
    CurrentSessionName = DefaultSessionName;
    bIsInitialized = false;
}

void UDruidsSageChatShell::NativeConstruct()
{
    Super::NativeConstruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::NativeConstruct - Constructing chat shell"));
    
    // Set up button events
    if (NewSessionButton)
    {
        NewSessionButton->OnClicked.AddDynamic(this, &UDruidsSageChatShell::OnNewSessionButtonClicked);
    }
    
    // Set up session list view
    SetupSessionListView();
    
    // Initialize sessions
    InitializeChatSessions();
    
    // Create default chat view
    CreateDefaultChatView();
    
    bIsInitialized = true;
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::NativeConstruct - Chat shell constructed successfully"));
}

void UDruidsSageChatShell::NativeDestruct()
{
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::NativeDestruct - Destructing chat shell"));
    
    // Save sessions before destruction
    SaveSessionsToDisk();
    
    // Clean up event bindings
    if (NewSessionButton)
    {
        NewSessionButton->OnClicked.RemoveAll(this);
    }
    
    if (SessionListView)
    {
        // Clean up session list view events
    }
    
    // Clean up chat view
    if (CurrentChatView)
    {
        CurrentChatView->RemoveFromParent();
        CurrentChatView = nullptr;
    }
    
    bIsInitialized = false;
    
    Super::NativeDestruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::NativeDestruct - Chat shell destructed"));
}

void UDruidsSageChatShell::SetChatRequestHandler(const TScriptInterface<IChatRequestHandler>& Handler)
{
    ChatRequestHandler = Handler;

    // Pass to current chat view
    if (CurrentChatView)
    {
        CurrentChatView->SetChatRequestHandler(Handler);
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::SetChatRequestHandler - Chat request handler set"));
}

void UDruidsSageChatShell::SetExtensionDelegator(const TScriptInterface<ISageExtensionDelegator>& Delegator)
{
    ExtensionDelegator = Delegator;

    // Pass to current chat view
    if (CurrentChatView)
    {
        CurrentChatView->SetExtensionDelegator(Delegator);
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::SetExtensionDelegator - Extension delegator set"));
}

void UDruidsSageChatShell::CreateNewChatSession(const FString& SessionName)
{
    FString NewSessionName = SessionName.IsEmpty() ? GenerateNewSessionName() : SessionName;
    
    if (!IsValidSessionName(NewSessionName))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatShell::CreateNewChatSession - Invalid session name: %s"), *NewSessionName);
        return;
    }
    
    if (ChatSessions.Contains(NewSessionName))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatShell::CreateNewChatSession - Session already exists: %s"), *NewSessionName);
        return;
    }
    
    // Add to sessions list
    ChatSessions.Add(NewSessionName);
    
    // Switch to new session
    SwitchToSession(NewSessionName);
    
    // Update UI
    OnSessionListUpdated();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::CreateNewChatSession - Created new session: %s"), *NewSessionName);
}

void UDruidsSageChatShell::SwitchToSession(const FString& SessionName)
{
    if (!ChatSessions.Contains(SessionName))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatShell::SwitchToSession - Session does not exist: %s"), *SessionName);
        return;
    }
    
    if (CurrentSessionName == SessionName)
    {
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::SwitchToSession - Already on session: %s"), *SessionName);
        return;
    }
    
    // Save current session state
    if (CurrentChatView && !CurrentSessionName.IsEmpty())
    {
        // Save current chat view state
    }
    
    // Switch session
    FString PreviousSession = CurrentSessionName;
    CurrentSessionName = SessionName;
    
    // Create or load chat view for new session
    InitializeChatSession(SessionName);
    
    // Update UI
    UpdateCurrentSessionLabel();
    
    // Notify events
    OnChatSessionChanged.Broadcast(SessionName);
    OnChatSessionChangedEvent(SessionName);
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::SwitchToSession - Switched from %s to %s"), *PreviousSession, *SessionName);
}

FString UDruidsSageChatShell::GetCurrentSessionName() const
{
    return CurrentSessionName;
}

void UDruidsSageChatShell::InitializeChatSessions()
{
    // Ensure sessions directory exists
    EnsureSessionsDirectoryExists();
    
    // Load sessions from disk
    LoadSessionsFromDisk();
    
    // Ensure we have at least the default session
    if (ChatSessions.IsEmpty())
    {
        ChatSessions.Add(DefaultSessionName);
        CurrentSessionName = DefaultSessionName;
    }
    
    // Call Blueprint event
    InitializeChatSessionsEvent();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::InitializeChatSessions - Initialized %d sessions"), ChatSessions.Num());
}

void UDruidsSageChatShell::InitializeChatSession(const FString& SessionName)
{
    if (!ChatSessions.Contains(SessionName))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatShell::InitializeChatSession - Session does not exist: %s"), *SessionName);
        return;
    }
    
    // Remove current chat view
    if (CurrentChatView)
    {
        CurrentChatView->RemoveFromParent();
        CurrentChatView = nullptr;
    }
    
    // Create new chat view for this session
    CurrentChatView = CreateChatViewInternal();
    
    if (CurrentChatView)
    {
        // Set session ID
        CurrentChatView->SetSessionID(SessionName);
        
        // Set handlers
        if (ChatRequestHandler.GetInterface())
        {
            CurrentChatView->SetChatRequestHandler(ChatRequestHandler);
        }
        
        if (ExtensionDelegator.GetInterface())
        {
            CurrentChatView->SetExtensionDelegator(ExtensionDelegator);
        }
        
        // Bind message sending event
        CurrentChatView->OnMessageSending.AddDynamic(this, &UDruidsSageChatShell::OnChatViewMessageSending);
        
        // Add to container
        if (ChatViewContainer)
        {
            ChatViewContainer->SetContent(CurrentChatView);
        }
        
        // Load chat history for this session
        CurrentChatView->LoadChatHistory();
        
        // Notify events
        OnChatViewCreated.Broadcast(CurrentChatView);
        OnChatViewCreatedEvent(CurrentChatView);
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::InitializeChatSession - Initialized session: %s"), *SessionName);
}

void UDruidsSageChatShell::LoadSessionsFromDisk()
{
    // Implementation would load session list from disk
    // For now, just ensure we have the default session
    if (!ChatSessions.Contains(DefaultSessionName))
    {
        ChatSessions.Add(DefaultSessionName);
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::LoadSessionsFromDisk - Sessions loaded from disk"));
}

void UDruidsSageChatShell::SaveSessionsToDisk()
{
    // Implementation would save session list to disk
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::SaveSessionsToDisk - Sessions saved to disk"));
}

FString UDruidsSageChatShell::GenerateNewSessionName()
{
    FDateTime Now = FDateTime::Now();
    FString BaseName = FString::Printf(TEXT("Session_%s"), *Now.ToString(TEXT("%Y%m%d_%H%M%S")));
    
    // Ensure uniqueness
    FString NewName = BaseName;
    int32 Counter = 1;
    while (ChatSessions.Contains(NewName))
    {
        NewName = FString::Printf(TEXT("%s_%d"), *BaseName, Counter++);
    }
    
    return NewName;
}

void UDruidsSageChatShell::OnSessionSelectionChanged(UObject* Item)
{
    // Handle session selection from list view
    // Implementation would depend on the data structure used for the list view
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::OnSessionSelectionChanged - Session selection changed"));
}

void UDruidsSageChatShell::OnNewSessionButtonClicked()
{
    CreateNewChatSession(FString());
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::OnNewSessionButtonClicked - New session button clicked"));
}

void UDruidsSageChatShell::OnChatViewMessageSending()
{
    OnMessageSending.Broadcast();
    OnMessageSendingEvent();
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::OnChatViewMessageSending - Message sending event received"));
}

void UDruidsSageChatShell::SetupSessionListView()
{
    if (SessionListView)
    {
        // Set up list view for sessions
        // Implementation would depend on the specific list view setup
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatShell::SetupSessionListView - Session list view set up"));
    }
}

void UDruidsSageChatShell::UpdateCurrentSessionLabel()
{
    if (CurrentSessionLabel)
    {
        CurrentSessionLabel->SetText(FText::FromString(CurrentSessionName));
    }
}

void UDruidsSageChatShell::CreateDefaultChatView()
{
    if (!CurrentChatView)
    {
        InitializeChatSession(CurrentSessionName);
    }
}

UDruidsSageChatView* UDruidsSageChatShell::CreateChatViewInternal()
{
    // Try Blueprint implementation first
    UDruidsSageChatView* BlueprintChatView = CreateChatViewWidget();
    if (BlueprintChatView)
    {
        return BlueprintChatView;
    }
    
    // Fall back to factory creation
    if (UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(this))
    {
        if (UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory())
        {
            return Factory->CreateChatView(this);
        }
    }
    
    UE_LOG(LogDruidsSage, Error, TEXT("UDruidsSageChatShell::CreateChatViewInternal - Failed to create chat view"));
    return nullptr;
}

void UDruidsSageChatShell::EnsureSessionsDirectoryExists()
{
    FString SessionsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SessionsDirectory);
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*SessionsPath))
    {
        PlatformFile.CreateDirectoryTree(*SessionsPath);
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatShell::EnsureSessionsDirectoryExists - Created sessions directory: %s"), *SessionsPath);
    }
}

FString UDruidsSageChatShell::GetSessionFilePath(const FString& SessionName) const
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), SessionsDirectory, SessionName + SessionFileExtension);
}

bool UDruidsSageChatShell::IsValidSessionName(const FString& SessionName) const
{
    return !SessionName.IsEmpty() && 
           !SessionName.Contains(TEXT("/")) && 
           !SessionName.Contains(TEXT("\\")) && 
           !SessionName.Contains(TEXT(":")) &&
           SessionName.Len() <= 100;
}
