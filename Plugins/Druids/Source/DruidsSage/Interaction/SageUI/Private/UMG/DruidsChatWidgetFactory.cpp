#include "UMG/DruidsChatWidgetFactory.h"
#include "UMG/DruidsSageChatShell.h"
#include "UMG/DruidsSageChatView.h"
#include "UMG/DruidsChatItemInterface.h"
#include "UMG/DruidsChatMasterConfiguration.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "Blueprint/WidgetTree.h"
#include "Engine/World.h"
#include "Engine/GameInstance.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "AssetRegistry/AssetData.h"
#include "LogDruids.h"

UDruidsChatStyleSettings::UDruidsChatStyleSettings()
{
    CategoryName = TEXT("Plugins");
    SectionName = TEXT("Druids Chat");
}

#if WITH_EDITOR
void UDruidsChatStyleSettings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);
    
    // Notify that settings have changed
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatStyleSettings::PostEditChangeProperty - Settings changed"));
}
#endif

const UDruidsChatStyleSettings* UDruidsChatStyleSettings::Get()
{
    return GetDefault<UDruidsChatStyleSettings>();
}

UDruidsChatWidgetFactory::UDruidsChatWidgetFactory()
{
    bIsInitialized = false;
}

UDruidsSageChatShell* UDruidsChatWidgetFactory::CreateChatShell(UObject* Outer)
{
    EnsureInitialized();
    
    TSubclassOf<UDruidsSageChatShell> ClassToUse = ChatShellOverride ? ChatShellOverride : DefaultChatShellClass;
    
    UDruidsSageChatShell* ChatShell = CreateWidgetFromClass<UDruidsSageChatShell>(
        ClassToUse, 
        DefaultChatShellClass, 
        Outer ? Outer : GetTransientPackage()
    );
    
    if (ChatShell)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateChatShell - Created chat shell of class %s"), 
            *ChatShell->GetClass()->GetName());
    }
    else
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateChatShell - Failed to create chat shell"));
    }
    
    return ChatShell;
}

UDruidsSageChatView* UDruidsChatWidgetFactory::CreateChatView(UObject* Outer)
{
    EnsureInitialized();
    
    TSubclassOf<UDruidsSageChatView> ClassToUse = ChatViewOverride ? ChatViewOverride : DefaultChatViewClass;
    
    UDruidsSageChatView* ChatView = CreateWidgetFromClass<UDruidsSageChatView>(
        ClassToUse, 
        DefaultChatViewClass, 
        Outer ? Outer : GetTransientPackage()
    );
    
    if (ChatView)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateChatView - Created chat view of class %s"), 
            *ChatView->GetClass()->GetName());
    }
    else
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateChatView - Failed to create chat view"));
    }
    
    return ChatView;
}

UUserWidget* UDruidsChatWidgetFactory::CreateChatItem(EDruidsSageChatRole Role, UObject* Outer)
{
    EnsureInitialized();
    
    TSubclassOf<UUserWidget> ClassToUse;
    TSubclassOf<UUserWidget> FallbackClass;
    
    switch (Role)
    {
    case EDruidsSageChatRole::Assistant:
        ClassToUse = AssistantChatItemOverride ? AssistantChatItemOverride : DefaultAssistantChatItemClass;
        FallbackClass = DefaultAssistantChatItemClass;
        break;
    case EDruidsSageChatRole::User:
    case EDruidsSageChatRole::System:
        ClassToUse = SimpleChatItemOverride ? SimpleChatItemOverride : DefaultSimpleChatItemClass;
        FallbackClass = DefaultSimpleChatItemClass;
        break;
    default:
        ClassToUse = DefaultSimpleChatItemClass;
        FallbackClass = DefaultSimpleChatItemClass;
        break;
    }
    
    UUserWidget* ChatItem = CreateWidgetFromClass<UUserWidget>(
        ClassToUse, 
        FallbackClass, 
        Outer ? Outer : GetTransientPackage()
    );
    
    if (ChatItem)
    {
        // Set the role if the widget implements the interface
        if (UDruidsChatItemBase* ChatItemBase = Cast<UDruidsChatItemBase>(ChatItem))
        {
            ChatItemBase->SetMessageRole(Role);
        }
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateChatItem - Created chat item of class %s for role %d"), 
            *ChatItem->GetClass()->GetName(), (int32)Role);
    }
    else
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateChatItem - Failed to create chat item for role %d"), (int32)Role);
    }
    
    return ChatItem;
}

void UDruidsChatWidgetFactory::DiscoverWidgetTemplates()
{
    const UDruidsChatStyleSettings* Settings = UDruidsChatStyleSettings::Get();
    if (!Settings || !Settings->bAutoDiscoverTemplates)
    {
        return;
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::DiscoverWidgetTemplates - Starting template discovery"));
    
    for (const FString& SearchPath : Settings->TemplateSearchPaths)
    {
        TArray<UClass*> FoundTemplates = FindWidgetTemplatesInPath(SearchPath, Settings->TemplatePrefix);
        
        for (UClass* TemplateClass : FoundTemplates)
        {
            FString ClassName = TemplateClass->GetName();
            
            // Try to match templates to widget types based on naming convention
            if (ClassName.Contains(TEXT("ChatShell")) && !ChatShellOverride)
            {
                if (ValidateWidgetTemplate(TemplateClass, UDruidsSageChatShell::StaticClass()))
                {
                    ChatShellOverride = TemplateClass;
                    UE_LOG(LogDruidsSage, Log, TEXT("Discovered ChatShell template: %s"), *ClassName);
                }
            }
            else if (ClassName.Contains(TEXT("ChatView")) && !ChatViewOverride)
            {
                if (ValidateWidgetTemplate(TemplateClass, UDruidsSageChatView::StaticClass()))
                {
                    ChatViewOverride = TemplateClass;
                    UE_LOG(LogDruidsSage, Log, TEXT("Discovered ChatView template: %s"), *ClassName);
                }
            }
            else if (ClassName.Contains(TEXT("AssistantChatItem")) && !AssistantChatItemOverride)
            {
                if (ValidateWidgetTemplate(TemplateClass, UDruidsChatItemBase::StaticClass()))
                {
                    AssistantChatItemOverride = TemplateClass;
                    UE_LOG(LogDruidsSage, Log, TEXT("Discovered AssistantChatItem template: %s"), *ClassName);
                }
            }
            else if (ClassName.Contains(TEXT("SimpleChatItem")) && !SimpleChatItemOverride)
            {
                if (ValidateWidgetTemplate(TemplateClass, UDruidsChatItemBase::StaticClass()))
                {
                    SimpleChatItemOverride = TemplateClass;
                    UE_LOG(LogDruidsSage, Log, TEXT("Discovered SimpleChatItem template: %s"), *ClassName);
                }
            }
        }
    }
}

bool UDruidsChatWidgetFactory::ValidateWidgetTemplate(UClass* WidgetClass, UClass* ExpectedBaseClass)
{
    if (!WidgetClass || !ExpectedBaseClass)
    {
        return false;
    }
    
    bool bIsValid = WidgetClass->IsChildOf(ExpectedBaseClass);
    
    if (!bIsValid)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetFactory::ValidateWidgetTemplate - Widget class %s is not a child of %s"), 
            *WidgetClass->GetName(), *ExpectedBaseClass->GetName());
    }
    
    return bIsValid;
}

TArray<UClass*> UDruidsChatWidgetFactory::FindWidgetTemplatesInPath(const FString& SearchPath, const FString& Prefix)
{
    TArray<UClass*> FoundClasses;
    
    // This is a simplified implementation - in a real scenario, you'd use the Asset Registry
    // to find Blueprint classes in the specified path
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::FindWidgetTemplatesInPath - Searching in %s for prefix %s"), 
        *SearchPath, *Prefix);
    
    // TODO: Implement actual asset registry search
    // For now, this is a placeholder that would be implemented with proper asset discovery
    
    return FoundClasses;
}

void UDruidsChatWidgetFactory::LoadWidgetClasses()
{
    const UDruidsChatStyleSettings* Settings = UDruidsChatStyleSettings::Get();
    if (!Settings)
    {
        return;
    }
    
    // Load override classes from settings
    if (!Settings->ChatShellClass.IsNull())
    {
        if (UClass* LoadedClass = LoadClassFromPath(Settings->ChatShellClass))
        {
            ChatShellOverride = LoadedClass;
        }
    }
    
    if (!Settings->ChatViewClass.IsNull())
    {
        if (UClass* LoadedClass = LoadClassFromPath(Settings->ChatViewClass))
        {
            ChatViewOverride = LoadedClass;
        }
    }
    
    if (!Settings->AssistantChatItemClass.IsNull())
    {
        if (UClass* LoadedClass = LoadClassFromPath(Settings->AssistantChatItemClass))
        {
            AssistantChatItemOverride = LoadedClass;
        }
    }
    
    if (!Settings->SimpleChatItemClass.IsNull())
    {
        if (UClass* LoadedClass = LoadClassFromPath(Settings->SimpleChatItemClass))
        {
            SimpleChatItemOverride = LoadedClass;
        }
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadWidgetClasses - Widget classes loaded from settings"));
}

void UDruidsChatWidgetFactory::ResetToDefaults()
{
    ChatShellOverride = nullptr;
    ChatViewOverride = nullptr;
    AssistantChatItemOverride = nullptr;
    SimpleChatItemOverride = nullptr;
    ActionRequestItemOverride = nullptr;
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::ResetToDefaults - Reset to default widget classes"));
}

template<typename T>
T* UDruidsChatWidgetFactory::CreateWidgetFromClass(TSubclassOf<T> WidgetClass, TSubclassOf<T> FallbackClass, UObject* Outer)
{
    UClass* ClassToUse = WidgetClass ? WidgetClass.Get() : (FallbackClass ? FallbackClass.Get() : nullptr);

    if (!ClassToUse)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - No valid class to create widget"));
        return nullptr;
    }

    // Check if the class is abstract (cannot be instantiated)
    if (ClassToUse->HasAnyClassFlags(CLASS_Abstract))
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Cannot create widget from abstract class %s. Please provide a concrete implementation."),
            *ClassToUse->GetName());
        return nullptr;
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Attempting to create widget of class %s"),
        *ClassToUse->GetName());

    // Try to get a valid world context for widget creation
    UWorld* World = nullptr;
    if (Outer)
    {
        World = GEngine->GetWorldFromContextObject(Outer, EGetWorldErrorMode::LogAndReturnNull);
    }

    // If we have a world, use it to create the widget
    if (World)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using World context"));
        T* Widget = CreateWidget<T>(World, ClassToUse);
        if (Widget)
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using World"));
            return Widget;
        }
        else
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Failed to create widget using World"));
        }
    }

    // Try to cast Outer to supported types for CreateWidget
    if (Outer)
    {
        // Try APlayerController
        if (APlayerController* PC = Cast<APlayerController>(Outer))
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using PlayerController"));
            T* Widget = CreateWidget<T>(PC, ClassToUse);
            if (Widget)
            {
                UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using PlayerController"));
                return Widget;
            }
        }

        // Try UGameInstance
        if (UGameInstance* GameInstance = Cast<UGameInstance>(Outer))
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using GameInstance"));
            T* Widget = CreateWidget<T>(GameInstance, ClassToUse);
            if (Widget)
            {
                UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using GameInstance"));
                return Widget;
            }
        }

        // Try UUserWidget
        if (UUserWidget* UserWidget = Cast<UUserWidget>(Outer))
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using UserWidget"));
            T* Widget = CreateWidget<T>(UserWidget, ClassToUse);
            if (Widget)
            {
                UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using UserWidget"));
                return Widget;
            }
        }

        // Try UWidgetTree
        if (UWidgetTree* WidgetTree = Cast<UWidgetTree>(Outer))
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using WidgetTree"));
            T* Widget = CreateWidget<T>(WidgetTree, ClassToUse);
            if (Widget)
            {
                UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using WidgetTree"));
                return Widget;
            }
        }
    }

    // As a fallback, try to find a valid world from GEngine
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
        {
            if (WorldContext.World() && WorldContext.World()->IsGameWorld())
            {
                UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Creating widget using fallback World from GEngine"));
                T* Widget = CreateWidget<T>(WorldContext.World(), ClassToUse);
                if (Widget)
                {
                    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using fallback World"));
                    return Widget;
                }
            }
        }
    }

    // Final fallback: use UWidgetBlueprintLibrary::Create
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Using UWidgetBlueprintLibrary::Create as final fallback"));
    UUserWidget* Widget = UWidgetBlueprintLibrary::Create(Outer, ClassToUse, nullptr);
    T* TypedWidget = Cast<T>(Widget);

    if (TypedWidget)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - Successfully created widget using UWidgetBlueprintLibrary"));
    }
    else
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::CreateWidgetFromClass - All widget creation methods failed"));
    }

    return TypedWidget;
}

UClass* UDruidsChatWidgetFactory::LoadClassFromPath(const FSoftClassPath& ClassPath)
{
    if (ClassPath.IsNull())
    {
        return nullptr;
    }
    
    UClass* LoadedClass = ClassPath.TryLoadClass<UUserWidget>();
    if (!LoadedClass)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetFactory::LoadClassFromPath - Failed to load class from path: %s"), 
            *ClassPath.ToString());
    }
    
    return LoadedClass;
}

bool UDruidsChatWidgetFactory::IsValidWidgetClass(UClass* WidgetClass, UClass* ExpectedBaseClass)
{
    return WidgetClass && ExpectedBaseClass && WidgetClass->IsChildOf(ExpectedBaseClass);
}

void UDruidsChatWidgetFactory::InitializeDefaultClasses()
{
    // Set default classes - these are the C++ implementations
    DefaultChatShellClass = UDruidsSageChatShell::StaticClass();
    DefaultChatViewClass = UDruidsSageChatView::StaticClass();

    // Use UUserWidget as default for chat items since UDruidsChatItemBase is abstract
    // In practice, these should be overridden with Blueprint implementations
    DefaultAssistantChatItemClass = UUserWidget::StaticClass();
    DefaultSimpleChatItemClass = UUserWidget::StaticClass();
    DefaultActionRequestItemClass = UUserWidget::StaticClass();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::InitializeDefaultClasses - Default classes initialized"));
    UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetFactory::InitializeDefaultClasses - Using UUserWidget as default for chat items. Consider creating Blueprint implementations that inherit from UDruidsChatItemBase."));
}

void UDruidsChatWidgetFactory::DiscoverMasterConfigurationBlueprint()
{
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::DiscoverMasterConfigurationBlueprint - Starting master configuration discovery"));

    // Try to find and load the best master configuration
    TSubclassOf<UDruidsChatMasterConfiguration> BestConfigClass = UDruidsChatMasterConfigurationUtility::GetBestMasterConfiguration();

    if (BestConfigClass)
    {
        if (LoadFromMasterConfiguration(BestConfigClass))
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::DiscoverMasterConfigurationBlueprint - Successfully loaded master configuration: %s"),
                *BestConfigClass->GetName());
        }
        else
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetFactory::DiscoverMasterConfigurationBlueprint - Failed to load master configuration: %s"),
                *BestConfigClass->GetName());
        }
    }
    else
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::DiscoverMasterConfigurationBlueprint - No master configuration found, using defaults"));
    }
}

bool UDruidsChatWidgetFactory::LoadFromMasterConfiguration(UClass* MasterConfigClass)
{
    if (!MasterConfigClass || !MasterConfigClass->IsChildOf(UDruidsChatMasterConfiguration::StaticClass()))
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Invalid master configuration class"));
        return false;
    }

    // Create an instance of the master configuration
    UDruidsChatMasterConfiguration* ConfigInstance = NewObject<UDruidsChatMasterConfiguration>(this, MasterConfigClass);
    if (!ConfigInstance)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Failed to create configuration instance"));
        return false;
    }

    // Validate the configuration
    if (!ConfigInstance->ValidateConfiguration())
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Configuration validation failed"));
        return false;
    }

    // Apply the configuration to our widget overrides
    bool bAnyOverrideApplied = false;

    if (ConfigInstance->CustomChatShellClass)
    {
        ChatShellOverride = ConfigInstance->CustomChatShellClass;
        bAnyOverrideApplied = true;
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied ChatShell override: %s"),
            *ConfigInstance->CustomChatShellClass->GetName());
    }

    if (ConfigInstance->CustomChatViewClass)
    {
        ChatViewOverride = ConfigInstance->CustomChatViewClass;
        bAnyOverrideApplied = true;
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied ChatView override: %s"),
            *ConfigInstance->CustomChatViewClass->GetName());
    }

    if (ConfigInstance->CustomAssistantChatItemClass)
    {
        AssistantChatItemOverride = ConfigInstance->CustomAssistantChatItemClass;
        bAnyOverrideApplied = true;
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied AssistantChatItem override: %s"),
            *ConfigInstance->CustomAssistantChatItemClass->GetName());
    }

    if (ConfigInstance->CustomSimpleChatItemClass)
    {
        SimpleChatItemOverride = ConfigInstance->CustomSimpleChatItemClass;
        bAnyOverrideApplied = true;
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied SimpleChatItem override: %s"),
            *ConfigInstance->CustomSimpleChatItemClass->GetName());
    }

    if (ConfigInstance->CustomActionRequestItemClass)
    {
        ActionRequestItemOverride = ConfigInstance->CustomActionRequestItemClass;
        bAnyOverrideApplied = true;
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied ActionRequestItem override: %s"),
            *ConfigInstance->CustomActionRequestItemClass->GetName());
    }

    // Call the Blueprint initialization event
    ConfigInstance->OnConfigurationLoaded();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Configuration '%s' loaded successfully. Overrides applied: %s"),
        *ConfigInstance->ConfigurationName, bAnyOverrideApplied ? TEXT("Yes") : TEXT("No"));

    return true;
}

TArray<UClass*> UDruidsChatWidgetFactory::FindMasterConfigurationBlueprints()
{
    TArray<UClass*> FoundClasses;

    // Use the utility class to find all master configurations
    TArray<TSubclassOf<UDruidsChatMasterConfiguration>> FoundConfigurations = UDruidsChatMasterConfigurationUtility::FindAllMasterConfigurations();

    // Convert to UClass* array
    for (const TSubclassOf<UDruidsChatMasterConfiguration>& ConfigClass : FoundConfigurations)
    {
        if (ConfigClass)
        {
            FoundClasses.Add(ConfigClass.Get());
        }
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::FindMasterConfigurationBlueprints - Found %d master configuration blueprints"),
        FoundClasses.Num());

    return FoundClasses;
}

void UDruidsChatWidgetFactory::EnsureInitialized()
{
    if (!bIsInitialized)
    {
        InitializeDefaultClasses();

        // Try master configuration first (highest priority)
        DiscoverMasterConfigurationBlueprint();

        // Then load from settings (medium priority)
        LoadWidgetClasses();

        // Finally, discover individual templates (lowest priority)
        DiscoverWidgetTemplates();

        bIsInitialized = true;

        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetFactory::EnsureInitialized - Factory initialized"));
    }
}
