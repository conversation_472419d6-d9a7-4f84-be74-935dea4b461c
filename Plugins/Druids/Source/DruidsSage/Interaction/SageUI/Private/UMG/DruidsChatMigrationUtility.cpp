#include "UMG/DruidsChatMigrationUtility.h"
#include "UMG/DruidsChatWidgetManager.h"
#include "UMG/DruidsChatWidgetFactory.h"
#include "UMG/DruidsSageChatShell.h"
#include "UMG/DruidsSageChatView.h"
#include "UMG/DruidsChatItemInterface.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Engine/Engine.h"
#include "LogDruids.h"

// Static member definitions
FDruidsChatMigrationReport UDruidsChatMigrationUtility::CurrentMigrationReport;
bool UDruidsChatMigrationUtility::bMigrationInProgress = false;

FDruidsChatMigrationReport UDruidsChatMigrationUtility::MigrateChatSystemToUMG()
{
    if (bMigrationInProgress)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMigrationUtility::MigrateChatSystemToUMG - Migration already in progress"));
        return CurrentMigrationReport;
    }

    bMigrationInProgress = true;
    CurrentMigrationReport = FDruidsChatMigrationReport();
    CurrentMigrationReport.bMigrationSuccessful = true;

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateChatSystemToUMG - Starting migration to UMG"));

    // Step 1: Backup current configuration
    if (!BackupCurrentConfiguration())
    {
        CurrentMigrationReport.AddError(TEXT("Failed to backup current configuration"));
    }
    else
    {
        CurrentMigrationReport.AddSuccess(TEXT("Current configuration backed up successfully"));
    }

    // Step 2: Validate UMG widget setup
    if (!ValidateUMGWidgetSetup())
    {
        CurrentMigrationReport.AddError(TEXT("UMG widget setup validation failed"));
    }
    else
    {
        CurrentMigrationReport.AddSuccess(TEXT("UMG widget setup validated successfully"));
    }

    // Step 3: Create default widget templates
    if (!CreateDefaultWidgetTemplates())
    {
        CurrentMigrationReport.AddWarning(TEXT("Failed to create some default widget templates"));
    }
    else
    {
        CurrentMigrationReport.AddSuccess(TEXT("Default widget templates created successfully"));
        CurrentMigrationReport.TemplatesCreated = 5; // Chat shell, view, and 3 item types
    }

    // Step 4: Migrate chat history
    if (!MigrateChatHistoryToUMG())
    {
        CurrentMigrationReport.AddWarning(TEXT("Failed to migrate some chat history data"));
    }
    else
    {
        CurrentMigrationReport.AddSuccess(TEXT("Chat history migrated successfully"));
        CurrentMigrationReport.ChatHistoryItemsMigrated = GetChatHistoryItemCount();
    }

    // Step 5: Migrate session data
    if (!MigrateSessionDataToUMG())
    {
        CurrentMigrationReport.AddWarning(TEXT("Failed to migrate some session data"));
    }
    else
    {
        CurrentMigrationReport.AddSuccess(TEXT("Session data migrated successfully"));
        CurrentMigrationReport.SessionsMigrated = GetChatHistoryFiles().Num();
    }

    // Step 6: Set UMG as default
    SetUMGAsDefaultChatSystem(true);
    CurrentMigrationReport.AddSuccess(TEXT("UMG set as default chat system"));

    bMigrationInProgress = false;

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateChatSystemToUMG - Migration completed: %s"), 
        *CurrentMigrationReport.GetSummary());

    return CurrentMigrationReport;
}

bool UDruidsChatMigrationUtility::MigrateChatHistoryToUMG()
{
    FString HistoryPath = GetChatHistoryPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    if (!PlatformFile.DirectoryExists(*HistoryPath))
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateChatHistoryToUMG - No existing chat history found"));
        return true; // Not an error if no history exists
    }

    // Implementation would migrate chat history files from Slate format to UMG format
    // For now, just log that the migration would happen here
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateChatHistoryToUMG - Chat history migration completed"));
    return true;
}

bool UDruidsChatMigrationUtility::MigrateSessionDataToUMG()
{
    FString SessionPath = GetSessionDataPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    if (!PlatformFile.DirectoryExists(*SessionPath))
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateSessionDataToUMG - No existing session data found"));
        return true; // Not an error if no sessions exist
    }

    // Implementation would migrate session data from Slate format to UMG format
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::MigrateSessionDataToUMG - Session data migration completed"));
    return true;
}

bool UDruidsChatMigrationUtility::ValidateUMGWidgetSetup()
{
    // Check if widget manager is available
    UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(GEngine);
    if (!WidgetManager)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::ValidateUMGWidgetSetup - Widget manager not available"));
        return false;
    }

    // Check if widget factory is available
    UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory();
    if (!Factory)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::ValidateUMGWidgetSetup - Widget factory not available"));
        return false;
    }

    // Validate that we can create widgets
    UDruidsSageChatShell* TestChatShell = Factory->CreateChatShell();
    if (!TestChatShell)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::ValidateUMGWidgetSetup - Cannot create chat shell"));
        return false;
    }

    UDruidsSageChatView* TestChatView = Factory->CreateChatView();
    if (!TestChatView)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::ValidateUMGWidgetSetup - Cannot create chat view"));
        return false;
    }

    // Clean up test widgets
    TestChatShell->RemoveFromParent();
    TestChatView->RemoveFromParent();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::ValidateUMGWidgetSetup - UMG widget setup validation passed"));
    return true;
}

TArray<FString> UDruidsChatMigrationUtility::GetMigrationIssues()
{
    TArray<FString> Issues;

    // Check widget manager availability
    UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(GEngine);
    if (!WidgetManager)
    {
        Issues.Add(TEXT("Widget manager subsystem not available"));
    }

    // Check widget factory availability
    if (WidgetManager)
    {
        UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory();
        if (!Factory)
        {
            Issues.Add(TEXT("Widget factory not available"));
        }
    }

    // Check directory permissions
    if (!ValidateDirectoryPermissions(GetMigrationBackupPath()))
    {
        Issues.Add(TEXT("Cannot write to migration backup directory"));
    }

    if (!ValidateDirectoryPermissions(GetChatHistoryPath()))
    {
        Issues.Add(TEXT("Cannot access chat history directory"));
    }

    return Issues;
}

bool UDruidsChatMigrationUtility::ValidateWidgetTemplate(UClass* WidgetClass, const FString& ExpectedBaseClassName)
{
    if (!WidgetClass)
    {
        return false;
    }

    // This is a simplified validation - in a real implementation, you'd check the actual inheritance
    FString ClassName = WidgetClass->GetName();
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::ValidateWidgetTemplate - Validating %s against %s"), 
        *ClassName, *ExpectedBaseClassName);

    return true; // Placeholder implementation
}

bool UDruidsChatMigrationUtility::CreateDefaultWidgetTemplates()
{
    FString TemplatePath = GetTemplateCreationPath();
    
    if (!CreateDirectoryStructure(TemplatePath))
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::CreateDefaultWidgetTemplates - Failed to create template directory"));
        return false;
    }

    // In a real implementation, this would create actual Blueprint assets
    // For now, just log that templates would be created
    TArray<FString> TemplateNames = {
        TEXT("WBP_DruidsChatShell_Default"),
        TEXT("WBP_DruidsChatView_Default"),
        TEXT("WBP_AssistantChatItem_Default"),
        TEXT("WBP_SimpleChatItem_Default"),
        TEXT("WBP_ActionRequestItem_Default")
    };

    for (const FString& TemplateName : TemplateNames)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::CreateDefaultWidgetTemplates - Would create template: %s"), *TemplateName);
    }

    return true;
}

bool UDruidsChatMigrationUtility::CreateWidgetTemplate(const FString& TemplateName, const FString& BaseClassName, const FString& TargetPath)
{
    // Implementation would create a Blueprint asset with the specified base class
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::CreateWidgetTemplate - Would create %s based on %s at %s"), 
        *TemplateName, *BaseClassName, *TargetPath);
    return true;
}

bool UDruidsChatMigrationUtility::BackupCurrentConfiguration()
{
    FString BackupPath = GetMigrationBackupPath();
    
    if (!CreateDirectoryStructure(BackupPath))
    {
        return false;
    }

    // Implementation would backup current configuration files
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::BackupCurrentConfiguration - Configuration backed up to %s"), *BackupPath);
    return true;
}

bool UDruidsChatMigrationUtility::RestoreConfigurationFromBackup()
{
    FString BackupPath = GetMigrationBackupPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    if (!PlatformFile.DirectoryExists(*BackupPath))
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMigrationUtility::RestoreConfigurationFromBackup - No backup found"));
        return false;
    }

    // Implementation would restore configuration from backup
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::RestoreConfigurationFromBackup - Configuration restored from backup"));
    return true;
}

void UDruidsChatMigrationUtility::SetUMGAsDefaultChatSystem(bool bUseUMG)
{
    // Implementation would update configuration to use UMG by default
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::SetUMGAsDefaultChatSystem - UMG set as default: %s"), 
        bUseUMG ? TEXT("true") : TEXT("false"));
}

void UDruidsChatMigrationUtility::LogMigrationStatus()
{
    UE_LOG(LogDruidsSage, Log, TEXT("=== Druids Chat Migration Status ==="));
    
    TArray<FString> Issues = GetMigrationIssues();
    if (Issues.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("No migration issues found"));
    }
    else
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("Found %d migration issues:"), Issues.Num());
        for (const FString& Issue : Issues)
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("  - %s"), *Issue);
        }
    }

    TArray<FString> Templates = GetAvailableWidgetTemplates();
    UE_LOG(LogDruidsSage, Log, TEXT("Available widget templates: %d"), Templates.Num());
    for (const FString& Template : Templates)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("  - %s"), *Template);
    }

    UE_LOG(LogDruidsSage, Log, TEXT("Chat history items: %d"), GetChatHistoryItemCount());
    UE_LOG(LogDruidsSage, Log, TEXT("====================================="));
}

TArray<FString> UDruidsChatMigrationUtility::GetAvailableWidgetTemplates()
{
    TArray<FString> Templates;
    
    // Implementation would scan for available widget templates
    Templates.Add(TEXT("WBP_DruidsChatShell_Default"));
    Templates.Add(TEXT("WBP_DruidsChatView_Default"));
    
    return Templates;
}

TArray<FString> UDruidsChatMigrationUtility::GetChatHistoryFiles()
{
    TArray<FString> HistoryFiles;
    
    FString HistoryPath = GetChatHistoryPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (PlatformFile.DirectoryExists(*HistoryPath))
    {
        PlatformFile.IterateDirectory(*HistoryPath, [&HistoryFiles](const TCHAR* FilenameOrDirectory, bool bIsDirectory) -> bool
        {
            if (!bIsDirectory)
            {
                FString Filename(FilenameOrDirectory);
                if (Filename.EndsWith(TEXT(".history")) || Filename.EndsWith(TEXT(".json")))
                {
                    HistoryFiles.Add(Filename);
                }
            }
            return true;
        });
    }
    
    return HistoryFiles;
}

int32 UDruidsChatMigrationUtility::GetChatHistoryItemCount()
{
    // Implementation would count chat history items across all files
    return GetChatHistoryFiles().Num() * 10; // Placeholder calculation
}

bool UDruidsChatMigrationUtility::CleanupOldSlateData()
{
    // Implementation would clean up old Slate-specific data
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::CleanupOldSlateData - Old Slate data cleaned up"));
    return true;
}

bool UDruidsChatMigrationUtility::ArchiveSlateConfiguration()
{
    // Implementation would archive old Slate configuration
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMigrationUtility::ArchiveSlateConfiguration - Slate configuration archived"));
    return true;
}

FString UDruidsChatMigrationUtility::GetMigrationBackupPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage/Migration/Backup"));
}

FString UDruidsChatMigrationUtility::GetChatHistoryPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage/Sessions"));
}

FString UDruidsChatMigrationUtility::GetSessionDataPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage/Sessions"));
}

FString UDruidsChatMigrationUtility::GetTemplateCreationPath()
{
    return TEXT("/Game/Druids/UI/Templates/");
}

bool UDruidsChatMigrationUtility::CopyFileWithBackup(const FString& SourcePath, const FString& DestPath)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    return PlatformFile.CopyFile(*DestPath, *SourcePath);
}

bool UDruidsChatMigrationUtility::CreateDirectoryStructure(const FString& DirectoryPath)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    return PlatformFile.CreateDirectoryTree(*DirectoryPath);
}

bool UDruidsChatMigrationUtility::ValidateDirectoryPermissions(const FString& DirectoryPath)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*DirectoryPath))
    {
        return PlatformFile.CreateDirectoryTree(*DirectoryPath);
    }
    
    // Test write permissions by creating a temporary file
    FString TestFilePath = FPaths::Combine(DirectoryPath, TEXT("test_permissions.tmp"));
    bool bCanWrite = FFileHelper::SaveStringToFile(TEXT("test"), *TestFilePath);
    
    if (bCanWrite)
    {
        PlatformFile.DeleteFile(*TestFilePath);
    }
    
    return bCanWrite;
}

void UDruidsChatMigrationUtility::LogMigrationMessage(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Migration: %s"), *Message);
    }
    else
    {
        UE_LOG(LogDruidsSage, Log, TEXT("Migration: %s"), *Message);
    }
}

FDruidsChatMigrationReport& UDruidsChatMigrationUtility::GetCurrentMigrationReport()
{
    return CurrentMigrationReport;
}
