#include "UMG/DruidsChatMasterConfiguration.h"
#include "Engine/Blueprint.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/AssetData.h"
#include "UObject/ConstructorHelpers.h"
#include "LogDruids.h"

UDruidsChatMasterConfiguration::UDruidsChatMasterConfiguration()
{
    // Set default values
    ConfigurationName = TEXT("Default Druids Chat Configuration");
    ConfigurationDescription = TEXT("Master configuration for Druids Chat widget overrides.");
    ConfigurationVersion = 1;
    Priority = 100;
    
    // Initialize custom widget classes to null (will use defaults)
    CustomChatShellClass = nullptr;
    CustomChatViewClass = nullptr;
    CustomAssistantChatItemClass = nullptr;
    CustomSimpleChatItemClass = nullptr;
    CustomActionRequestItemClass = nullptr;
}

bool UDruidsChatMasterConfiguration::ValidateConfiguration() const
{
    bool bIsValid = true;
    
    // Validate ChatShell class if set
    if (CustomChatShellClass)
    {
        if (!IsValidWidgetClass(CustomChatShellClass, UDruidsSageChatShell::StaticClass()))
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - CustomChatShellClass is not a valid UDruidsSageChatShell subclass"));
            bIsValid = false;
        }
    }
    
    // Validate ChatView class if set
    if (CustomChatViewClass)
    {
        if (!IsValidWidgetClass(CustomChatViewClass, UDruidsSageChatView::StaticClass()))
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - CustomChatViewClass is not a valid UDruidsSageChatView subclass"));
            bIsValid = false;
        }
    }
    
    // Validate chat item classes if set
    if (CustomAssistantChatItemClass)
    {
        if (!IsValidWidgetClass(CustomAssistantChatItemClass, UUserWidget::StaticClass()))
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - CustomAssistantChatItemClass is not a valid UUserWidget subclass"));
            bIsValid = false;
        }
    }
    
    if (CustomSimpleChatItemClass)
    {
        if (!IsValidWidgetClass(CustomSimpleChatItemClass, UUserWidget::StaticClass()))
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - CustomSimpleChatItemClass is not a valid UUserWidget subclass"));
            bIsValid = false;
        }
    }
    
    if (CustomActionRequestItemClass)
    {
        if (!IsValidWidgetClass(CustomActionRequestItemClass, UUserWidget::StaticClass()))
        {
            UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - CustomActionRequestItemClass is not a valid UUserWidget subclass"));
            bIsValid = false;
        }
    }
    
    // Call Blueprint validation if available
    if (bIsValid)
    {
        // Note: OnValidateCustomConfiguration is BlueprintImplementableEvent, 
        // so we can't call it directly from C++. It will be called by Blueprint if overridden.
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfiguration::ValidateConfiguration - Configuration '%s' validation result: %s"), 
        *ConfigurationName, bIsValid ? TEXT("VALID") : TEXT("INVALID"));
    
    return bIsValid;
}

FString UDruidsChatMasterConfiguration::GetConfigurationSummary() const
{
    FString Summary = FString::Printf(TEXT("=== Druids Chat Master Configuration ===\n"));
    Summary += FString::Printf(TEXT("Name: %s\n"), *ConfigurationName);
    Summary += FString::Printf(TEXT("Description: %s\n"), *ConfigurationDescription);
    Summary += FString::Printf(TEXT("Version: %d\n"), ConfigurationVersion);
    Summary += FString::Printf(TEXT("Priority: %d\n"), Priority);
    Summary += FString::Printf(TEXT("Custom Widgets Defined: %d\n"), GetCustomWidgetCount());
    Summary += TEXT("\nWidget Overrides:\n");
    
    Summary += GetWidgetClassSummary(CustomChatShellClass, TEXT("ChatShell"));
    Summary += GetWidgetClassSummary(CustomChatViewClass, TEXT("ChatView"));
    Summary += GetWidgetClassSummary(CustomAssistantChatItemClass, TEXT("AssistantChatItem"));
    Summary += GetWidgetClassSummary(CustomSimpleChatItemClass, TEXT("SimpleChatItem"));
    Summary += GetWidgetClassSummary(CustomActionRequestItemClass, TEXT("ActionRequestItem"));
    
    Summary += TEXT("==========================================");
    
    return Summary;
}

bool UDruidsChatMasterConfiguration::HasAnyCustomWidgets() const
{
    return CustomChatShellClass != nullptr ||
           CustomChatViewClass != nullptr ||
           CustomAssistantChatItemClass != nullptr ||
           CustomSimpleChatItemClass != nullptr ||
           CustomActionRequestItemClass != nullptr;
}

int32 UDruidsChatMasterConfiguration::GetCustomWidgetCount() const
{
    int32 Count = 0;
    
    if (CustomChatShellClass) Count++;
    if (CustomChatViewClass) Count++;
    if (CustomAssistantChatItemClass) Count++;
    if (CustomSimpleChatItemClass) Count++;
    if (CustomActionRequestItemClass) Count++;
    
    return Count;
}

bool UDruidsChatMasterConfiguration::IsValidWidgetClass(UClass* WidgetClass, UClass* ExpectedBaseClass) const
{
    if (!WidgetClass || !ExpectedBaseClass)
    {
        return false;
    }
    
    return WidgetClass->IsChildOf(ExpectedBaseClass);
}

FString UDruidsChatMasterConfiguration::GetWidgetClassSummary(UClass* WidgetClass, const FString& WidgetType) const
{
    if (WidgetClass)
    {
        return FString::Printf(TEXT("  %s: %s\n"), *WidgetType, *WidgetClass->GetName());
    }
    else
    {
        return FString::Printf(TEXT("  %s: (Default)\n"), *WidgetType);
    }
}

#if WITH_EDITOR
void UDruidsChatMasterConfiguration::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);
    
    // Validate configuration when properties change in editor
    if (PropertyChangedEvent.Property)
    {
        FString PropertyName = PropertyChangedEvent.Property->GetName();
        
        if (PropertyName.Contains(TEXT("Custom")) && PropertyName.Contains(TEXT("Class")))
        {
            // A widget class was changed, validate it
            bool bIsValid = ValidateConfiguration();
            
            if (!bIsValid)
            {
                UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfiguration::PostEditChangeProperty - Configuration validation failed after changing %s"), 
                    *PropertyName);
            }
        }
    }
}
#endif

// ========================================
// UDruidsChatMasterConfigurationUtility Implementation
// ========================================

TArray<TSubclassOf<UDruidsChatMasterConfiguration>> UDruidsChatMasterConfigurationUtility::FindAllMasterConfigurations()
{
    TArray<TSubclassOf<UDruidsChatMasterConfiguration>> FoundConfigurations;
    
    // Get asset registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    
    // Find all assets that inherit from UDruidsChatMasterConfiguration
    TArray<FAssetData> AssetDataArray = FindMasterConfigurationAssets();
    
    for (const FAssetData& AssetData : AssetDataArray)
    {
        if (IsValidMasterConfigurationAsset(AssetData))
        {
            // Load the Blueprint and get its generated class
            if (UBlueprint* Blueprint = Cast<UBlueprint>(AssetData.GetAsset()))
            {
                if (UClass* GeneratedClass = Blueprint->GeneratedClass)
                {
                    if (GeneratedClass->IsChildOf(UDruidsChatMasterConfiguration::StaticClass()))
                    {
                        FoundConfigurations.Add(GeneratedClass);
                        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::FindAllMasterConfigurations - Found configuration: %s"), 
                            *GeneratedClass->GetName());
                    }
                }
            }
        }
    }
    
    // Sort by priority (highest first)
    FoundConfigurations.Sort([](const TSubclassOf<UDruidsChatMasterConfiguration>& A, const TSubclassOf<UDruidsChatMasterConfiguration>& B)
    {
        UDruidsChatMasterConfiguration* DefaultA = A->GetDefaultObject<UDruidsChatMasterConfiguration>();
        UDruidsChatMasterConfiguration* DefaultB = B->GetDefaultObject<UDruidsChatMasterConfiguration>();
        
        return DefaultA->Priority > DefaultB->Priority;
    });
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::FindAllMasterConfigurations - Found %d master configurations"), 
        FoundConfigurations.Num());
    
    return FoundConfigurations;
}

TSubclassOf<UDruidsChatMasterConfiguration> UDruidsChatMasterConfigurationUtility::GetBestMasterConfiguration()
{
    TArray<TSubclassOf<UDruidsChatMasterConfiguration>> AllConfigurations = FindAllMasterConfigurations();
    
    // Return the highest priority configuration (first in sorted array)
    if (AllConfigurations.Num() > 0)
    {
        TSubclassOf<UDruidsChatMasterConfiguration> BestConfig = AllConfigurations[0];
        UDruidsChatMasterConfiguration* DefaultObject = BestConfig->GetDefaultObject<UDruidsChatMasterConfiguration>();
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::GetBestMasterConfiguration - Selected configuration: %s (Priority: %d)"), 
            *DefaultObject->ConfigurationName, DefaultObject->Priority);
        
        return BestConfig;
    }
    
    UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatMasterConfigurationUtility::GetBestMasterConfiguration - No master configurations found"));
    return nullptr;
}

UDruidsChatMasterConfiguration* UDruidsChatMasterConfigurationUtility::CreateBestMasterConfigurationInstance(UObject* Outer)
{
    TSubclassOf<UDruidsChatMasterConfiguration> BestConfigClass = GetBestMasterConfiguration();
    
    if (!BestConfigClass)
    {
        return nullptr;
    }
    
    if (!Outer)
    {
        Outer = GetTransientPackage();
    }
    
    UDruidsChatMasterConfiguration* ConfigInstance = NewObject<UDruidsChatMasterConfiguration>(Outer, BestConfigClass);
    
    if (ConfigInstance)
    {
        // Validate the configuration
        if (ConfigInstance->ValidateConfiguration())
        {
            UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::CreateBestMasterConfigurationInstance - Created and validated configuration instance: %s"), 
                *ConfigInstance->ConfigurationName);
            
            // Call Blueprint initialization event
            ConfigInstance->OnConfigurationLoaded();
            
            return ConfigInstance;
        }
        else
        {
            UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatMasterConfigurationUtility::CreateBestMasterConfigurationInstance - Configuration validation failed for: %s"), 
                *ConfigInstance->ConfigurationName);
        }
    }
    
    return nullptr;
}

bool UDruidsChatMasterConfigurationUtility::ValidateMasterConfigurationClass(TSubclassOf<UDruidsChatMasterConfiguration> ConfigClass)
{
    if (!ConfigClass)
    {
        return false;
    }

    // Check if it's a valid subclass
    if (!ConfigClass->IsChildOf(UDruidsChatMasterConfiguration::StaticClass()))
    {
        return false;
    }

    // Create a temporary instance to validate
    UDruidsChatMasterConfiguration* TempInstance = NewObject<UDruidsChatMasterConfiguration>(GetTransientPackage(), ConfigClass);
    if (!TempInstance)
    {
        return false;
    }

    bool bIsValid = TempInstance->ValidateConfiguration();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::ValidateMasterConfigurationClass - Validation result for %s: %s"),
        *ConfigClass->GetName(), bIsValid ? TEXT("VALID") : TEXT("INVALID"));

    return bIsValid;
}

TArray<FAssetData> UDruidsChatMasterConfigurationUtility::FindMasterConfigurationAssets()
{
    TArray<FAssetData> FoundAssets;

    // Get asset registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Create filter to find Blueprint assets
    FARFilter Filter;
    Filter.ClassPaths.Add(UBlueprint::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;

    // Get all Blueprint assets
    TArray<FAssetData> AllBlueprints;
    AssetRegistry.GetAssets(Filter, AllBlueprints);

    // Filter for Blueprints that inherit from UDruidsChatMasterConfiguration
    for (const FAssetData& AssetData : AllBlueprints)
    {
        // Check if this Blueprint's parent class is UDruidsChatMasterConfiguration or a subclass
        const FString ParentClassPath = AssetData.GetTagValueRef<FString>("ParentClass");

        if (ParentClassPath.Contains(TEXT("DruidsChatMasterConfiguration")))
        {
            FoundAssets.Add(AssetData);
            UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatMasterConfigurationUtility::FindMasterConfigurationAssets - Found potential configuration asset: %s"),
                *AssetData.AssetName.ToString());
        }
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatMasterConfigurationUtility::FindMasterConfigurationAssets - Found %d potential master configuration assets"),
        FoundAssets.Num());

    return FoundAssets;
}

bool UDruidsChatMasterConfigurationUtility::IsValidMasterConfigurationAsset(const FAssetData& AssetData)
{
    // Basic validation - check if it's a Blueprint
    if (AssetData.AssetClassPath != UBlueprint::StaticClass()->GetClassPathName())
    {
        return false;
    }

    // Check if the Blueprint has the correct parent class
    const FString ParentClassPath = AssetData.GetTagValueRef<FString>("ParentClass");
    if (!ParentClassPath.Contains(TEXT("DruidsChatMasterConfiguration")))
    {
        return false;
    }

    // Additional validation could be added here
    // For example, checking specific tags or metadata

    return true;
}
