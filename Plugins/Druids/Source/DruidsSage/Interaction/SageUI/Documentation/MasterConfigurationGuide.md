# Druids Chat Master Configuration Guide

## Overview

The Druids Chat system now supports a **Master Configuration Blueprint** approach that allows you to define all your custom widget overrides in a single Blueprint that is automatically discovered by the C++ code. This eliminates the need to modify C++ code or settings files.

## How It Works

1. **Create a single Master Configuration Blueprint** that inherits from `UDruidsChatMasterConfiguration`
2. **Set your custom widget classes** in the Blueprint's properties
3. **The C++ system automatically discovers and uses** your configuration at runtime
4. **All customization is done entirely in the Editor** - no C++ changes needed

## Step-by-Step Setup

### Step 1: Create Your Custom Widget Blueprints

First, create your custom widget Blueprints that inherit from the appropriate base classes:

#### ChatShell Blueprint
- **Create**: Blueprint Class → User Interface → Widget Blueprint
- **Parent Class**: `Druids Sage Chat Shell` (UDruidsSageChatShell)
- **Name**: `WBP_MyCustomChatShell`
- **Required Widgets** (use exact names for BindWidget):
  - `SessionListView` (List View)
  - `ChatViewContainer` (Border)
  - `NewSessionButton` (Button)
  - `CurrentSessionLabel` (Text Block)

#### ChatView Blueprint
- **Create**: Blueprint Class → User Interface → Widget Blueprint
- **Parent Class**: `Druids Sage Chat View` (UDruidsSageChatView)
- **Name**: `WBP_MyCustomChatView`
- **Required Widgets** (use exact names for BindWidget):
  - `ChatScrollBox` (Scroll Box)
  - `ChatItemContainer` (Vertical Box)
  - `MessageInputBox` (Multi Line Editable Text Box)
  - `SendButton` (Button)
  - `ClearButton` (Button)

#### Chat Item Blueprints
- **Create**: Blueprint Class → User Interface → Widget Blueprint
- **Parent Class**: `Druids Chat Item Base` (UDruidsChatItemBase)
- **Names**: 
  - `WBP_MyCustomAssistantChatItem`
  - `WBP_MyCustomSimpleChatItem`
  - `WBP_MyCustomActionRequestItem`

### Step 2: Create the Master Configuration Blueprint

1. **Create a new Blueprint Class**
   - Right-click in Content Browser → Blueprint Class
   - Search for and select `Druids Chat Master Configuration`
   - Name it something like `BP_DruidsChatMasterConfig` or `WBP_DruidsChatConfig`

2. **Configure the Master Blueprint**
   - Open your master configuration Blueprint
   - In the **Details Panel**, find the **Widget Overrides** section
   - Set each custom widget class:
     - **Custom Chat Shell Class**: `WBP_MyCustomChatShell`
     - **Custom Chat View Class**: `WBP_MyCustomChatView`
     - **Custom Assistant Chat Item Class**: `WBP_MyCustomAssistantChatItem`
     - **Custom Simple Chat Item Class**: `WBP_MyCustomSimpleChatItem`
     - **Custom Action Request Item Class**: `WBP_MyCustomActionRequestItem`

3. **Set Configuration Info** (optional but recommended):
   - **Configuration Name**: "My Custom Chat Configuration"
   - **Configuration Description**: "Custom chat widgets for my project"
   - **Priority**: 100 (higher numbers take precedence if multiple configs exist)

### Step 3: That's It!

The system will automatically:
- **Discover** your master configuration Blueprint at runtime
- **Validate** all your widget classes
- **Apply** your custom widgets to the chat system
- **Log** the discovery and loading process

## Advanced Features

### Multiple Configurations

If you have multiple master configuration Blueprints, the system will:
- Find all of them
- Sort by **Priority** (highest first)
- Use the highest priority configuration
- Log which configuration was selected

### Blueprint Events

You can override these Blueprint events in your master configuration:

#### OnConfigurationLoaded
- Called when your configuration is loaded by the factory
- Use for custom initialization logic

#### OnValidateCustomConfiguration
- Called during validation
- Return `true` if your custom validation passes
- Use for complex validation rules

### Validation

The system automatically validates:
- Widget classes inherit from correct base classes
- All set widget classes are valid
- Configuration can be instantiated
- Custom Blueprint validation (if implemented)

### Debugging

Check the **Output Log** for messages like:
```
LogDruidsSage: UDruidsChatMasterConfigurationUtility::GetBestMasterConfiguration - Selected configuration: BP_DruidsChatMasterConfig_C (Priority: 100)
LogDruidsSage: UDruidsChatWidgetFactory::LoadFromMasterConfiguration - Applied ChatShell override: WBP_MyCustomChatShell_C
```

## Example Project Structure

```
Content/
├── UI/
│   ├── Chat/
│   │   ├── WBP_MyCustomChatShell
│   │   ├── WBP_MyCustomChatView
│   │   ├── WBP_MyCustomAssistantChatItem
│   │   ├── WBP_MyCustomSimpleChatItem
│   │   └── WBP_MyCustomActionRequestItem
│   └── Config/
│       └── BP_DruidsChatMasterConfig
```

## Fallback Behavior

The system uses a priority hierarchy:

1. **Master Configuration** (Highest Priority)
   - Automatically discovered Blueprint configurations
   - Sorted by Priority property

2. **Settings Configuration** (Medium Priority)
   - Widget classes set in UDruidsChatStyleSettings
   - Configured through Project Settings

3. **Individual Template Discovery** (Lower Priority)
   - Individual widget Blueprints found by naming convention
   - Searches specific paths for specific prefixes

4. **Default C++ Classes** (Fallback)
   - Built-in C++ widget implementations
   - Always available as final fallback

## Troubleshooting

### Configuration Not Found
- Ensure your Blueprint inherits from `UDruidsChatMasterConfiguration`
- Check that the Blueprint compiles without errors
- Verify the Blueprint is saved

### Widget Classes Not Applied
- Check the Output Log for validation errors
- Ensure your custom widgets inherit from the correct base classes
- Verify widget class references are set correctly in the master configuration

### Multiple Configurations
- Check Priority values - higher numbers take precedence
- Look for log messages showing which configuration was selected
- Only one configuration will be used (the highest priority one)

## Benefits

✅ **Editor-Only Workflow**: No C++ changes required
✅ **Automatic Discovery**: No manual registration needed  
✅ **Single Source of Truth**: All overrides in one place
✅ **Priority System**: Handle multiple configurations gracefully
✅ **Validation**: Automatic validation with helpful error messages
✅ **Debugging**: Comprehensive logging for troubleshooting
✅ **Fallback Support**: Graceful degradation if configuration fails
