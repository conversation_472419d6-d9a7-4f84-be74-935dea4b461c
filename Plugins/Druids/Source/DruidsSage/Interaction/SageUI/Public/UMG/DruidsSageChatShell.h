#pragma once

#include "CoreMinimal.h"
#include "UMG/DruidsChatWidgetBase.h"

#include "IChatRequestHandler.h"
#include "ISageExtensionDelegator.h"

#include "DruidsSageChatShell.generated.h"

// Forward declarations
class UDruidsSageChatView;
class UListView;
class UBorder;
class UButton;
class UTextBlock;

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnMessageSending);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChatSessionChanged, const FString&, SessionName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChatViewCreated, UDruidsSageChatView*, ChatView);

/**
 * UMG version of the chat shell widget
 * Manages chat sessions and contains the main chat view
 */
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatShell : public UDruidsChatWidgetBase
{
    GENERATED_BODY()

public:
    UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer);

    // Event delegates for Blueprint customization
    UPROPERTY(BlueprintAssignable, Category = "Chat Shell Events")
    FOnMessageSending OnMessageSending;

    UPROPERTY(BlueprintAssignable, Category = "Chat Shell Events")
    FOnChatSessionChanged OnChatSessionChanged;

    UPROPERTY(BlueprintAssignable, Category = "Chat Shell Events")
    FOnChatViewCreated OnChatViewCreated;

    // Blueprint events for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Events")
    void OnChatSessionChangedEvent(const FString& SessionName);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Events")
    void OnMessageSendingEvent();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Events")
    void OnChatViewCreatedEvent(UDruidsSageChatView* ChatView);

    // Public interface methods
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetChatRequestHandler(const TScriptInterface<IChatRequestHandler>& Handler);

    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetExtensionDelegator(const TScriptInterface<ISageExtensionDelegator>& Delegator);
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    UDruidsSageChatView* GetCurrentChatView() const { return CurrentChatView; }

    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void CreateNewChatSession(const FString& SessionName);

    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SwitchToSession(const FString& SessionName);

    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    TArray<FString> GetChatSessions() const { return ChatSessions; }

    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    FString GetCurrentSessionName() const;

protected:
    // Widget references (can be bound in Blueprint Designer)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UListView* SessionListView;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UBorder* ChatViewContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UButton* NewSessionButton;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UTextBlock* CurrentSessionLabel;

    // Internal state
    UPROPERTY(BlueprintReadOnly, Category = "Chat Shell State")
    UDruidsSageChatView* CurrentChatView;
    
    UPROPERTY()
    TScriptInterface<IChatRequestHandler> ChatRequestHandler;

    UPROPERTY()
    TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
    
    UPROPERTY(BlueprintReadOnly, Category = "Sessions")
    TArray<FString> ChatSessions;

    UPROPERTY(BlueprintReadOnly, Category = "Sessions")
    FString CurrentSessionName;

    // Blueprint implementable functions for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Customization")
    UDruidsSageChatView* CreateChatViewWidget();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Customization")
    void InitializeChatSessionsEvent();

    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Customization")
    void OnSessionListUpdated();

    // Native implementations with Blueprint override support
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    // Session management methods
    UFUNCTION(BlueprintCallable, Category = "Session Management")
    void InitializeChatSessions();
    
    UFUNCTION(BlueprintCallable, Category = "Session Management")
    void InitializeChatSession(const FString& SessionName);

    UFUNCTION(BlueprintCallable, Category = "Session Management")
    void LoadSessionsFromDisk();

    UFUNCTION(BlueprintCallable, Category = "Session Management")
    void SaveSessionsToDisk();

    UFUNCTION(BlueprintCallable, Category = "Session Management")
    FString GenerateNewSessionName();

    // Event handlers
    UFUNCTION()
    void OnSessionSelectionChanged(UObject* Item);

    UFUNCTION()
    void OnNewSessionButtonClicked();

    UFUNCTION()
    void OnChatViewMessageSending();

    // Internal helpers
    void SetupSessionListView();
    void UpdateCurrentSessionLabel();
    void CreateDefaultChatView();
    UDruidsSageChatView* CreateChatViewInternal();

private:
    // Constants
    static const FString DefaultSessionName;
    static const FString SessionsDirectory;
    static const FString SessionFileExtension;

    // Internal state
    bool bIsInitialized = false;
    
    // Helper methods
    void EnsureSessionsDirectoryExists();
    FString GetSessionFilePath(const FString& SessionName) const;
    bool IsValidSessionName(const FString& SessionName) const;
};
