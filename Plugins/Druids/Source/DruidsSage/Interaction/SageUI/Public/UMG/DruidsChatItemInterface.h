#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "DruidsSageChatTypes.h"
#include "UMG/DruidsChatWidgetBase.h"
#include "DruidsChatItemInterface.generated.h"

// Forward declarations
struct FDruidsSageChatMessage;

UINTERFACE(BlueprintType)
class SAGEUI_API UDruidsChatItemInterface : public UInterface
{
    GENERATED_BODY()
};

/**
 * Interface for all chat item widgets
 * Defines the contract that all chat items must implement
 */
class SAGEUI_API IDruidsChatItemInterface
{
    GENERATED_BODY()

public:
    // Core interface methods that must be implemented
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void SetMessageContent(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void UpdateFromStreamingContent(const FString& PartialContent);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    EDruidsSageChatRole GetMessageRole() const;
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void OnActionButtonClicked(const FString& ActionData);
    
    // Optional customization events
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void OnStyleApplied(const FDruidsChatStyle& Style);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void OnMessageUpdated();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void OnStreamingStarted();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item Interface")
    void OnStreamingCompleted();

    // Native interface methods (can be overridden in C++)
    virtual void SetMessageContent_Native(const FDruidsSageChatMessage& Message) {}
    virtual void UpdateFromStreamingContent_Native(const FString& PartialContent) {}
    virtual EDruidsSageChatRole GetMessageRole_Native() const { return EDruidsSageChatRole::User; }
    virtual void OnActionButtonClicked_Native(const FString& ActionData) {}
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChatItemActionClicked, const FString&, ActionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChatItemUpdated);

/**
 * Base class for all chat item widgets
 * Provides common functionality and implements the chat item interface
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatItemBase : public UDruidsChatWidgetBase, public IDruidsChatItemInterface
{
    GENERATED_BODY()

public:
    UDruidsChatItemBase(const FObjectInitializer& ObjectInitializer);

    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Chat Item Events")
    FOnChatItemActionClicked OnActionClicked;

    UPROPERTY(BlueprintAssignable, Category = "Chat Item Events")
    FOnChatItemUpdated OnItemUpdated;

    // Interface implementation with Blueprint Native Events
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item Interface")
    void SetMessageContent(const FDruidsSageChatMessage& Message);
    virtual void SetMessageContent_Implementation(const FDruidsSageChatMessage& Message);

    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item Interface")
    void UpdateFromStreamingContent(const FString& PartialContent);
    virtual void UpdateFromStreamingContent_Implementation(const FString& PartialContent);

    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item Interface")
    EDruidsSageChatRole GetMessageRole() const;
    virtual EDruidsSageChatRole GetMessageRole_Implementation() const;

    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item Interface")
    void OnActionButtonClicked(const FString& ActionData);
    virtual void OnActionButtonClicked_Implementation(const FString& ActionData);

    // Public accessors
    UFUNCTION(BlueprintCallable, Category = "Chat Item")
    const FDruidsSageChatMessage& GetCurrentMessage() const { return CurrentMessage; }

    UFUNCTION(BlueprintCallable, Category = "Chat Item")
    bool IsStreaming() const { return bIsStreaming; }

    UFUNCTION(BlueprintCallable, Category = "Chat Item")
    void SetMessageRole(EDruidsSageChatRole Role);

protected:
    // Common widget bindings (can be bound in Blueprint)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* RoleLabel;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class URichTextBlock* MessageContent;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UBorder* MessageContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UHorizontalBox* ActionButtonContainer;

    // Message data
    UPROPERTY(BlueprintReadOnly, Category = "Message Data")
    FDruidsSageChatMessage CurrentMessage;
    
    UPROPERTY(BlueprintReadOnly, Category = "Message Data")
    EDruidsSageChatRole MessageRole = EDruidsSageChatRole::User;

    UPROPERTY(BlueprintReadOnly, Category = "Message Data")
    FString StreamingContent;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsStreaming = false;

    // Widget lifecycle overrides
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;

    // Style application
    virtual void ApplyStyle(const FDruidsChatStyle& Style) override;

    // Helper methods
    UFUNCTION(BlueprintCallable, Category = "Chat Item Helpers")
    void ApplyRoleBasedStyling();

    UFUNCTION(BlueprintCallable, Category = "Chat Item Helpers")
    void UpdateMessageDisplay();

    UFUNCTION(BlueprintCallable, Category = "Chat Item Helpers")
    void CreateActionButtons(const TArray<FString>& ActionData);

    UFUNCTION(BlueprintCallable, Category = "Chat Item Helpers")
    FString GetRoleDisplayName() const;

    UFUNCTION(BlueprintCallable, Category = "Chat Item Helpers")
    FLinearColor GetRoleColor() const;

    // Internal button click handler (no parameters to match delegate signature)
    UFUNCTION()
    void OnActionButtonClickedInternal();

private:
    // Internal helpers
    void InitializeChatItem();
    void CleanupChatItem();
    void NotifyItemUpdated();
};
