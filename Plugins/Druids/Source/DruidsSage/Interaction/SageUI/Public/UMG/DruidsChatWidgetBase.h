#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DruidsSageChatTypes.h"
#include "DruidsChatWidgetBase.generated.h"

USTRUCT(BlueprintType)
struct SAGEUI_API FDruidsChatStyle
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FLinearColor PrimaryColor = FLinearColor(0.2f, 0.4f, 0.8f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FLinearColor SecondaryColor = FLinearColor(0.3f, 0.3f, 0.3f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FLinearColor AccentColor = FLinearColor(0.1f, 0.8f, 0.3f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FLinearColor BackgroundColor = FLinearColor(0.1f, 0.1f, 0.1f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FLinearColor TextColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Typography")
    FSlateFontInfo DefaultFont;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Typography")
    FSlateFontInfo HeaderFont;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacing")
    FMargin DefaultPadding = FMargin(8.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacing")
    float DefaultSpacing = 4.0f;

    FDruidsChatStyle()
    {
        DefaultFont = FCoreStyle::GetDefaultFontStyle("Regular", 12);
        HeaderFont = FCoreStyle::GetDefaultFontStyle("Bold", 14);
    }
};

/**
 * Base class for all Druids Chat UMG widgets
 * Provides common functionality and styling support
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatWidgetBase : public UUserWidget
{
    GENERATED_BODY()

public:
    UDruidsChatWidgetBase(const FObjectInitializer& ObjectInitializer);

    // Style management
    UFUNCTION(BlueprintImplementableEvent, Category = "Druids Chat Style")
    void OnStyleChanged(const FDruidsChatStyle& NewStyle);

    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    virtual void ApplyStyle(const FDruidsChatStyle& Style);

    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    const FDruidsChatStyle& GetCurrentStyle() const { return CurrentStyle; }

    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    void SetStyle(const FDruidsChatStyle& NewStyle);

    // Widget lifecycle
    UFUNCTION(BlueprintImplementableEvent, Category = "Druids Chat Lifecycle")
    void OnWidgetInitialized();

    UFUNCTION(BlueprintImplementableEvent, Category = "Druids Chat Lifecycle")
    void OnWidgetDestroyed();

protected:
    // Current style applied to this widget
    UPROPERTY(BlueprintReadOnly, Category = "Style")
    FDruidsChatStyle CurrentStyle;

    // Widget initialization flag
    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsInitialized = false;

    // Native overrides
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;

    // Style application helpers
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    void ApplyColorToWidget(UWidget* Widget, const FLinearColor& Color, const FString& PropertyName = TEXT("ColorAndOpacity"));

    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    void ApplyFontToTextWidget(UWidget* TextWidget, const FSlateFontInfo& Font);

    UFUNCTION(BlueprintCallable, Category = "Druids Chat Style")
    void ApplyPaddingToWidget(UWidget* Widget, const FMargin& WidgetPadding);

private:
    // Internal initialization
    void InitializeWidget();
    void CleanupWidget();
};
