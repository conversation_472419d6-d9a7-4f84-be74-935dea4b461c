#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/DeveloperSettings.h"
#include "DruidsChatMigrationUtility.generated.h"

USTRUCT(BlueprintType)
struct SAGEUI_API FDruidsChatMigrationReport
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    bool bMigrationSuccessful = false;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    TArray<FString> SuccessMessages;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    TArray<FString> WarningMessages;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    TArray<FString> ErrorMessages;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    int32 ChatHistoryItemsMigrated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    int32 SessionsMigrated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Migration Report")
    int32 TemplatesCreated = 0;

    void AddSuccess(const FString& Message)
    {
        SuccessMessages.Add(Message);
    }

    void AddWarning(const FString& Message)
    {
        WarningMessages.Add(Message);
    }

    void AddError(const FString& Message)
    {
        ErrorMessages.Add(Message);
        bMigrationSuccessful = false;
    }

    FString GetSummary() const
    {
        return FString::Printf(TEXT("Migration %s: %d successes, %d warnings, %d errors"),
            bMigrationSuccessful ? TEXT("Successful") : TEXT("Failed"),
            SuccessMessages.Num(),
            WarningMessages.Num(),
            ErrorMessages.Num());
    }
};

/**
 * Utility class for migrating from Slate to UMG chat system
 * Provides tools for data migration, validation, and template creation
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatMigrationUtility : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // Main migration functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static FDruidsChatMigrationReport MigrateChatSystemToUMG();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool MigrateChatHistoryToUMG();
    
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool MigrateSessionDataToUMG();

    // Validation functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool ValidateUMGWidgetSetup();
    
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static TArray<FString> GetMigrationIssues();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool ValidateWidgetTemplate(UClass* WidgetClass, const FString& ExpectedBaseClassName);

    // Template creation functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool CreateDefaultWidgetTemplates();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool CreateWidgetTemplate(const FString& TemplateName, const FString& BaseClassName, const FString& TargetPath);

    // Configuration functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool BackupCurrentConfiguration();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool RestoreConfigurationFromBackup();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static void SetUMGAsDefaultChatSystem(bool bUseUMG = true);

    // Diagnostic functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static void LogMigrationStatus();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static TArray<FString> GetAvailableWidgetTemplates();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static TArray<FString> GetChatHistoryFiles();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static int32 GetChatHistoryItemCount();

    // Cleanup functions
    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool CleanupOldSlateData();

    UFUNCTION(BlueprintCallable, Category = "Migration", meta = (CallInEditor = "true"))
    static bool ArchiveSlateConfiguration();

protected:
    // Internal helper functions
    static FString GetMigrationBackupPath();
    static FString GetChatHistoryPath();
    static FString GetSessionDataPath();
    static FString GetTemplateCreationPath();

    static bool CopyFileWithBackup(const FString& SourcePath, const FString& DestPath);
    static bool CreateDirectoryStructure(const FString& DirectoryPath);
    static bool ValidateDirectoryPermissions(const FString& DirectoryPath);

    static void LogMigrationMessage(const FString& Message, bool bIsError = false);
    static FDruidsChatMigrationReport& GetCurrentMigrationReport();

private:
    // Migration state
    static FDruidsChatMigrationReport CurrentMigrationReport;
    static bool bMigrationInProgress;
};
