#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/DeveloperSettings.h"
#include "DruidsSageChatTypes.h"
#include "DruidsChatWidgetFactory.generated.h"

// Forward declarations
class UDruidsSageChatShell;
class UDruidsSageChatView;
class UDruidsChatItemBase;
class UUserWidget;

/**
 * Settings class for chat widget style configuration
 */
UCLASS(Config = Game, DefaultConfig, meta = (DisplayName = "Druids Chat Style"))
class SAGEUI_API UDruidsChatStyleSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UDruidsChatStyleSettings();

    // Widget override classes
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides", 
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatShell"))
    FSoftClassPath ChatShellClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatView"))
    FSoftClassPath ChatViewClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath AssistantChatItemClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath SimpleChatItemClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath ActionRequestItemClass;
    
    // Style configuration
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor PrimaryColor = FLinearColor(0.2f, 0.4f, 0.8f);
    
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor SecondaryColor = FLinearColor(0.3f, 0.3f, 0.3f);
    
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor AccentColor = FLinearColor(0.1f, 0.8f, 0.3f);
    
    // Auto-discovery settings
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    bool bAutoDiscoverTemplates = true;
    
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    TArray<FString> TemplateSearchPaths = {
        "/Game/Druids/UI/Templates/",
        "/Game/UI/Druids/",
        "/Game/Widgets/Chat/"
    };
    
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    FString TemplatePrefix = "WBP_Druids";
    
    // Performance settings
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    int32 MaxChatHistoryItems = 1000;
    
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    bool bEnableWidgetPooling = true;
    
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    int32 WidgetPoolSize = 50;

#if WITH_EDITOR
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

    // Helper methods
    UFUNCTION(BlueprintCallable, Category = "Settings")
    static const UDruidsChatStyleSettings* Get();
};

/**
 * Factory class for creating chat widgets with Blueprint override support
 */
UCLASS(BlueprintType, Blueprintable, Config = Game)
class SAGEUI_API UDruidsChatWidgetFactory : public UObject
{
    GENERATED_BODY()

public:
    UDruidsChatWidgetFactory();

    // Widget creation methods with Blueprint override support
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UDruidsSageChatShell* CreateChatShell(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UDruidsSageChatView* CreateChatView(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, UObject* Outer = nullptr);

    // Blueprint override configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UDruidsSageChatShell> ChatShellOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UDruidsSageChatView> ChatViewOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> AssistantChatItemOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> SimpleChatItemOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> ActionRequestItemOverride;

    // Master Configuration Blueprint Discovery
    UFUNCTION(BlueprintCallable, Category = "Master Configuration")
    void DiscoverMasterConfigurationBlueprint();

    UFUNCTION(BlueprintCallable, Category = "Master Configuration")
    bool LoadFromMasterConfiguration(UClass* MasterConfigClass);

    // Template discovery and validation
    UFUNCTION(BlueprintCallable, Category = "Template Discovery")
    void DiscoverWidgetTemplates();

    UFUNCTION(BlueprintCallable, Category = "Template Validation")
    bool ValidateWidgetTemplate(UClass* WidgetClass, UClass* ExpectedBaseClass);

    UFUNCTION(BlueprintCallable, Category = "Template Discovery")
    TArray<UClass*> FindWidgetTemplatesInPath(const FString& SearchPath, const FString& Prefix);

    UFUNCTION(BlueprintCallable, Category = "Template Discovery")
    TArray<UClass*> FindMasterConfigurationBlueprints();

    // Factory configuration
    UFUNCTION(BlueprintCallable, Category = "Factory Configuration")
    void LoadWidgetClasses();

    UFUNCTION(BlueprintCallable, Category = "Factory Configuration")
    void ResetToDefaults();

protected:
    // Default widget classes (fallbacks)
    UPROPERTY()
    TSubclassOf<UDruidsSageChatShell> DefaultChatShellClass;
    
    UPROPERTY()
    TSubclassOf<UDruidsSageChatView> DefaultChatViewClass;
    
    UPROPERTY()
    TSubclassOf<UUserWidget> DefaultAssistantChatItemClass;
    
    UPROPERTY()
    TSubclassOf<UUserWidget> DefaultSimpleChatItemClass;
    
    UPROPERTY()
    TSubclassOf<UUserWidget> DefaultActionRequestItemClass;

    // Internal helper methods
    template<typename T>
    T* CreateWidgetFromClass(TSubclassOf<T> WidgetClass, TSubclassOf<T> FallbackClass, UObject* Outer);

    UClass* LoadClassFromPath(const FSoftClassPath& ClassPath);
    bool IsValidWidgetClass(UClass* WidgetClass, UClass* ExpectedBaseClass);
    void InitializeDefaultClasses();

private:
    // Initialization flag
    bool bIsInitialized = false;
    
    void EnsureInitialized();
};
