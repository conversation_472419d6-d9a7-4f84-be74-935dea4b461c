#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/DataAsset.h"
#include "UMG/DruidsChatWidgetBase.h"
#include "UMG/DruidsSageChatShell.h"
#include "UMG/DruidsSageChatView.h"
#include "UMG/DruidsChatItemInterface.h"
#include "DruidsChatMasterConfiguration.generated.h"

/**
 * Master Configuration Blueprint Class
 * 
 * This is the single Blueprint that contains all widget class overrides for the Druids Chat system.
 * Create a Blueprint inheriting from this class and set all your custom widget classes here.
 * The system will automatically discover and use this configuration.
 * 
 * Usage:
 * 1. Create a Blueprint class inheriting from UDruidsChatMasterConfiguration
 * 2. Name it something like "BP_DruidsChatMasterConfig" or "WBP_DruidsChatConfig"
 * 3. Set all your custom widget classes in the Blueprint
 * 4. The C++ system will automatically find and use it
 */
UCLASS(BlueprintType, Blueprintable, Abstract)
class SAGEUI_API UDruidsChatMasterConfiguration : public UDataAsset
{
    GENERATED_BODY()

public:
    UDruidsChatMasterConfiguration();

    // ========================================
    // WIDGET CLASS OVERRIDES
    // ========================================
    
    /** 
     * Custom ChatShell widget class
     * Should inherit from UDruidsSageChatShell
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Overrides", 
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatShell", 
                ToolTip = "Custom ChatShell widget class. Should inherit from UDruidsSageChatShell."))
    TSubclassOf<UDruidsSageChatShell> CustomChatShellClass;
    
    /** 
     * Custom ChatView widget class
     * Should inherit from UDruidsSageChatView
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatView",
                ToolTip = "Custom ChatView widget class. Should inherit from UDruidsSageChatView."))
    TSubclassOf<UDruidsSageChatView> CustomChatViewClass;
    
    /** 
     * Custom Assistant Chat Item widget class
     * Should inherit from UDruidsChatItemBase
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget",
                ToolTip = "Custom Assistant Chat Item widget class. Should inherit from UDruidsChatItemBase."))
    TSubclassOf<UUserWidget> CustomAssistantChatItemClass;
    
    /** 
     * Custom Simple Chat Item widget class
     * Should inherit from UDruidsChatItemBase
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget",
                ToolTip = "Custom Simple Chat Item widget class. Should inherit from UDruidsChatItemBase."))
    TSubclassOf<UUserWidget> CustomSimpleChatItemClass;
    
    /** 
     * Custom Action Request Item widget class
     * Should inherit from UDruidsChatItemBase
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget",
                ToolTip = "Custom Action Request Item widget class. Should inherit from UDruidsChatItemBase."))
    TSubclassOf<UUserWidget> CustomActionRequestItemClass;

    // ========================================
    // CONFIGURATION METADATA
    // ========================================
    
    /** 
     * Configuration name for identification
     * Used for logging and debugging
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration Info",
        meta = (ToolTip = "Name of this configuration for identification purposes."))
    FString ConfigurationName = TEXT("Default Druids Chat Configuration");
    
    /** 
     * Configuration description
     * Describe what this configuration provides
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration Info",
        meta = (MultiLine = true,
                ToolTip = "Description of what this configuration provides."))
    FString ConfigurationDescription = TEXT("Master configuration for Druids Chat widget overrides.");
    
    /** 
     * Configuration version for compatibility tracking
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration Info",
        meta = (ToolTip = "Version of this configuration for compatibility tracking."))
    int32 ConfigurationVersion = 1;
    
    /** 
     * Priority for this configuration when multiple are found
     * Higher values take precedence
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration Info",
        meta = (ToolTip = "Priority when multiple configurations are found. Higher values take precedence."))
    int32 Priority = 100;

    // ========================================
    // VALIDATION AND UTILITY METHODS
    // ========================================
    
    /** 
     * Validate that all set widget classes are valid
     * Returns true if configuration is valid
     */
    UFUNCTION(BlueprintCallable, Category = "Configuration Validation")
    bool ValidateConfiguration() const;
    
    /** 
     * Get a summary of this configuration
     * Useful for debugging and logging
     */
    UFUNCTION(BlueprintCallable, Category = "Configuration Info")
    FString GetConfigurationSummary() const;
    
    /** 
     * Check if this configuration has any custom widgets set
     * Returns false if all widget classes are null
     */
    UFUNCTION(BlueprintCallable, Category = "Configuration Validation")
    bool HasAnyCustomWidgets() const;
    
    /** 
     * Get the number of custom widgets defined in this configuration
     */
    UFUNCTION(BlueprintCallable, Category = "Configuration Info")
    int32 GetCustomWidgetCount() const;

    // ========================================
    // BLUEPRINT EVENTS FOR CUSTOMIZATION
    // ========================================
    
    /** 
     * Called when this configuration is loaded by the factory
     * Override in Blueprint to perform custom initialization
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Configuration Events")
    void OnConfigurationLoaded();
    
    /** 
     * Called to validate custom logic in Blueprint
     * Override to add custom validation rules
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Configuration Events")
    bool OnValidateCustomConfiguration();

protected:
    // Internal validation helpers
    bool IsValidWidgetClass(UClass* WidgetClass, UClass* ExpectedBaseClass) const;
    FString GetWidgetClassSummary(UClass* WidgetClass, const FString& WidgetType) const;

#if WITH_EDITOR
    // Editor-only validation
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};

/**
 * Static utility class for Master Configuration discovery and management
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatMasterConfigurationUtility : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    /** 
     * Find all Master Configuration Blueprints in the project
     * Returns array of configuration classes sorted by priority
     */
    UFUNCTION(BlueprintCallable, Category = "Master Configuration Utility")
    static TArray<TSubclassOf<UDruidsChatMasterConfiguration>> FindAllMasterConfigurations();
    
    /** 
     * Get the highest priority Master Configuration Blueprint
     * Returns null if none found
     */
    UFUNCTION(BlueprintCallable, Category = "Master Configuration Utility")
    static TSubclassOf<UDruidsChatMasterConfiguration> GetBestMasterConfiguration();
    
    /** 
     * Create an instance of the best Master Configuration
     * Returns null if none found or creation failed
     */
    UFUNCTION(BlueprintCallable, Category = "Master Configuration Utility")
    static UDruidsChatMasterConfiguration* CreateBestMasterConfigurationInstance(UObject* Outer = nullptr);
    
    /** 
     * Validate a Master Configuration class
     * Returns true if the class is valid and usable
     */
    UFUNCTION(BlueprintCallable, Category = "Master Configuration Utility")
    static bool ValidateMasterConfigurationClass(TSubclassOf<UDruidsChatMasterConfiguration> ConfigClass);

private:
    // Internal discovery helpers
    static TArray<FAssetData> FindMasterConfigurationAssets();
    static bool IsValidMasterConfigurationAsset(const FAssetData& AssetData);
};
