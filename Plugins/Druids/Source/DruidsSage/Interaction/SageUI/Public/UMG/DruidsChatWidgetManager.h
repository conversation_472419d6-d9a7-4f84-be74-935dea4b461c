#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "DruidsChatWidgetManager.generated.h"

// Forward declarations
class UDruidsChatWidgetFactory;

/**
 * Manager for chat widget creation and lifecycle
 * Provides centralized access to the widget factory and manages widget pooling
 */
UCLASS()
class SAGEUI_API UDruidsChatWidgetManager : public UObject
{
    GENERATED_BODY()

public:
    // Manual initialization method
    void InitializeManager();

    // Manual cleanup method
    void CleanupManager();

    // Static access method
    static UDruidsChatWidgetManager* Get(const UObject* WorldContext = nullptr);

    // Widget factory management
    void SetWidgetFactory(UDruidsChatWidgetFactory* NewFactory);
    UDruidsChatWidgetFactory* GetWidgetFactory();
    void RefreshWidgetTemplates();

    // Widget pooling (for performance optimization)
    void EnableWidgetPooling(bool bEnable);
    bool IsWidgetPoolingEnabled() const { return bWidgetPoolingEnabled; }
    void SetPoolSize(int32 NewPoolSize);
    int32 GetPoolSize() const { return WidgetPoolSize; }

    // Widget lifecycle management
    void RegisterActiveWidget(UUserWidget* Widget);
    void UnregisterActiveWidget(UUserWidget* Widget);
    void CleanupInactiveWidgets();
    int32 GetActiveWidgetCount() const;

    // Configuration management
    void ReloadConfiguration();
    void SaveConfiguration();

    // Debug and diagnostics
    void LogWidgetFactoryStatus();
    TArray<FString> GetActiveWidgetInfo();

protected:
    // Widget factory instance
    UPROPERTY()
    UDruidsChatWidgetFactory* CurrentFactory;

    // Widget pooling settings
    UPROPERTY()
    bool bWidgetPoolingEnabled = true;

    UPROPERTY()
    int32 WidgetPoolSize = 50;

    // Active widget tracking
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ActiveWidgets;

    // Widget pools for different types
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatItemPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatViewPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatShellPool;

private:
    // Internal initialization
    void LoadDefaultFactory();
    void InitializeWidgetPools();
    void CleanupWidgetPools();

    // Pool management helpers
    UUserWidget* GetPooledWidget(TArray<TWeakObjectPtr<UUserWidget>>& Pool, UClass* WidgetClass);
    void ReturnWidgetToPool(UUserWidget* Widget, TArray<TWeakObjectPtr<UUserWidget>>& Pool);
    void CleanupPool(TArray<TWeakObjectPtr<UUserWidget>>& Pool);

    // Configuration helpers
    void LoadConfigurationFromSettings();
    void ApplyConfigurationToFactory();

    // Static instance management
    static TWeakObjectPtr<UDruidsChatWidgetManager> CachedInstance;
};
